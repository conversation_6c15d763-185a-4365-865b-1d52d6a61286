import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { ColumnDef } from '@tanstack/react-table';
import axios from 'axios';
import { t } from 'i18next';
import { AlertTriangle, ArrowUpDown, Pencil, Trash2 } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { useDispatch, useSelector } from 'react-redux';
import { z } from 'zod';
import { Badge } from '../../../../../@/components/ui/badge';
import { Button } from '../../../../../@/components/ui/Common/Elements/Button/ButtonSort';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../../../../../@/components/ui/Common/Elements/Form/Form';
import { Input } from '../../../../../@/components/ui/Common/Elements/Input/Input';
import { Label } from '../../../../../@/components/ui/Common/Elements/Label/Label';
import {
  RadioGroup,
  RadioGroupItem,
} from '../../../../../@/components/ui/Common/Elements/RadioGroup/radio-group';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '../../../../../@/components/ui/Common/Elements/Select/Select';
import MultipleSelector from '../../../../../@/components/ui/MultipleSelector';
import { Switch } from '../../../../../@/components/ui/switch';
import { Textarea } from '../../../../../@/components/ui/textarea';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../../../../../@/components/ui/tooltip';
import plusSign from '../../../../../assets/plusSign.svg';
import { universalConsentManagementActions } from '../../../../../redux/reducers/UniversalConsentManagement/universal-consent-management-slice';
import { RootState } from '../../../../../redux/store';
import {
  ConsentFrequencyProperties,
  ConsentPurposeRecordProperties,
  ConsentPurposeStep,
  CreateNewConsentPurposeProperties,
  NewConsentProperties,
  NewPIIProperties,
  NewProcessingCategoryProperties,
  NewProcessingPurposeProperties,
  PIILabelOptionProperties,
  PIILabelRecordsProperties,
  PIIMap,
  PIIMapping,
  Process,
  ProcessingCategoryRecordProperties,
  ProcessingPurposeRecordProperties,
  Vendor,
} from '../../../../../types/universal-consent-management';
import { convertString } from '../../../../common/CommonHelperFunctions';
import {
  activate_new_collection_template,
  create_consent_purpose_mapping,
  create_new_consent_purpose,
  create_new_pii_label,
  delete_consent_purpose,
  fetchPiiLabelData,
  get_collecton_template_step_2,
  get_consent_purpose_records,
  get_frequency,
  get_processing_purpose_category_records,
  get_processing_purpose_records,
  getProcessList,
  getVendorList,
} from '../../../../common/services/universal-consent-management';
import ShadcnDialog from '../../../../common/shadcn-dialog';
import DynamicTable from '../../../../common/ShadcnDynamicTable/dynamic-table';
import { useStepper } from '../../../../common/Stepper/use-stepper';
import styles from './AddCollectionTemplate.module.css';
import { StepperFormActions } from './consent-builder-stepper';
import ConsentWarning from './ConsentWarning';
import { AddPIIModalFormData, AddPIIModalFormSchema } from './FormSchemas/add-pii-modal-schema';
import {
  ConsentConfigFormData,
  ConsentConfigFormSchema,
} from './FormSchemas/consent-configuration-schema';

const badgeClass = 'text-[white] bg-primary';

function AddPIILabelStepTwo() {
  const { nextStep, activeStep } = useStepper();
  const dispatch = useDispatch();
  const savedDataRetention = useSelector(
    (state: RootState) => state.UCM.CollectionTemplateData.dataRetention
  );

  const form_id = useSelector(
    (state: RootState) => state.UCM.CollectionTemplateData.source.form_id
  );

  const preference_center_id = useSelector(
    (state: RootState) => state.UCM.CollectionTemplateData.preferenceForm.selected_preference_id
  );

  const languages = useSelector(
    (state: RootState) => state.UCM.CollectionTemplateData.preferenceForm.available_translations
  );

  //! VARIABLES
  const customer_id =
    useSelector((state: RootState) => state.auth.login.login_details?.customer_id) ?? 0;
  const collection_template_id = useSelector(
    (state: RootState) => state?.UCM?.CollectionTemplateData?.id
  );
  const subject_identity_type_id = useSelector(
    (state: RootState) => state?.UCM?.CollectionTemplateData?.basicInfo?.subject_identity_type_id
  );
  const disableADDPII = useRef<boolean>(false);
  const columns: ColumnDef<PIIMapping>[] = [
    {
      accessorKey: 'pii_name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            PII
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const name: string = row?.original?.pii_name;

        return <div className="">{name ?? '-'}</div>;
      },
    },
    {
      accessorKey: 'vendor_name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            Vendor Name
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const vendor_details: { id: number; name: string }[] | undefined = row?.original?.vendor;
        return (
          <div className="flex w-full flex-row flex-wrap gap-2">
            {vendor_details?.map((vendor) => {
              return <Badge className={badgeClass}>{vendor?.name}</Badge>;
            })}
          </div>
        );
      },
    },
    {
      accessorKey: 'process_name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            Process Name
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const process_details: { id: number; name: string }[] | undefined = row?.original?.process;
        return (
          <div className="flex w-full flex-row flex-wrap gap-2">
            {process_details?.map((process) => {
              return <Badge className={badgeClass}>{process?.name}</Badge>;
            })}
          </div>
        );
      },
    },
    {
      header: 'Action',
      accessorKey: 'action',
      cell: ({ row }) => {
        const handleEdit = (event: React.MouseEvent) => {
          event?.stopPropagation();
          addPIIModalFormData?.setValue('pii', row?.original?.pii_id?.toString());
          addPIIModalFormData?.setValue('vendor', row?.original?.vendor);
          addPIIModalFormData?.setValue('process', row?.original?.process);
          setOpenAddPIIModal(true);
          disableADDPII.current = true;
        };

        const handleDelete = (event: React.MouseEvent) => {
          event?.stopPropagation();

          setPIIMappingData((prev) => {
            const updatedMap = new Map(prev);
            updatedMap.delete(row.original.pii_id); // Directly delete entry

            return updatedMap;
          });
        };

        return (
          <div className="flex w-full flex-row gap-4">
            <Pencil onClick={handleEdit} /> <Trash2 onClick={handleDelete} />
          </div>
        );
      },
    },
  ];

  const consentRecordsPIIColumn: ColumnDef<PIIMap>[] = [
    {
      accessorKey: 'pii_name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            PII
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const name: string = row?.original?.pii_label_name;

        return <div className="">{name ?? '-'}</div>;
      },
    },
    {
      accessorKey: 'vendor_name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            Vendor Name
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const vendor_name: string[] = row?.original?.ct_cp_map
          ?.map((item) => item?.vendor_name)
          .filter((vendor): vendor is string => vendor !== null);

        return (
          <div className="flex w-full flex-row flex-wrap gap-2">
            {vendor_name?.map((vendor) => (
              <Badge className={badgeClass} key={vendor}>
                {vendor}
              </Badge>
            ))}
          </div>
        );
      },
    },
    {
      accessorKey: 'process_name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            Process Name
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const process_name: string[] = row?.original?.ct_cp_map
          ?.map((item) => item?.process_name)
          .filter((process): process is string => process !== null);

        return (
          <div className="flex w-full flex-row flex-wrap gap-2">
            {process_name?.map((process) => (
              <Badge className={badgeClass} key={process}>
                {process}
              </Badge>
            ))}
          </div>
        );
      },
    },
  ];

  const consentRecordsColumns: ColumnDef<ConsentPurposeStep>[] = [
    {
      accessorKey: 'consent_purpose_name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            Consent Purpose
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const rowData: ConsentPurposeStep = row?.original;

        return <div className="h-full w-full">{rowData?.consent_purpose_name}</div>;
      },
    },
    {
      accessorKey: 'processing_purpose_name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            Processing Purpose
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const rowData: ConsentPurposeStep = row?.original;

        return <div className="h-full w-full">{rowData?.processing_purpose_name}</div>;
      },
    },
    {
      accessorKey: 'processing_purpose_category_name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            Processing Purpose Category
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const rowData: ConsentPurposeStep = row?.original;

        return <div className="h-full w-full">{rowData?.processing_purpose_category_name}</div>;
      },
    },
    {
      accessorKey: 'Frequency',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            Frequency
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const rowData: ConsentPurposeStep = row?.original;
        return (
          <div className="h-full w-full">
            {rowData?.pii_map?.length > 0 &&
            rowData?.pii_map[0]?.ct_cp_map?.length > 0 &&
            rowData?.pii_map[0]?.ct_cp_map[0]?.frequency
              ? rowData?.pii_map[0]?.ct_cp_map[0]?.frequency
              : 'NA'}
          </div>
        );
      },
    },
    {
      accessorKey: 'default_opt_out',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            Default Opt Out
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const rowData: ConsentPurposeStep = row?.original;
        return (
          <div className="h-full w-full">
            {rowData?.pii_map?.length > 0 &&
            rowData?.pii_map[0]?.ct_cp_map?.length > 0 &&
            rowData?.pii_map[0]?.ct_cp_map[0]?.default_opt_out
              ? 'Yes'
              : 'No'}
          </div>
        );
      },
    },
    {
      accessorKey: 'consent_lifetime',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            Consent Lifetime
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const rowData: ConsentPurposeStep = row?.original;
        return (
          <div className="h-full w-full">
            {rowData?.pii_map?.length > 0 &&
            rowData?.pii_map[0]?.ct_cp_map?.length > 0 &&
            rowData?.pii_map[0]?.ct_cp_map[0]?.consent_lifetime
              ? `${rowData?.pii_map[0]?.ct_cp_map[0]?.consent_lifetime} days`
              : 'NA'}
          </div>
        );
      },
    },
    {
      accessorKey: 'compulsory_consent',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            Compulsory Consent
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const rowData: ConsentPurposeStep = row?.original;
        return (
          <div className="h-full w-full">
            {rowData?.pii_map?.length > 0 && rowData?.pii_map[0]?.ct_cp_map?.length > 0
              ? rowData?.pii_map[0]?.ct_cp_map[0]?.compulsory_consent
                ? 'Yes'
                : 'No'
              : 'NA'}
          </div>
        );
      },
    },
    {
      accessorKey: 'Action',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            // onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            Action
          </Button>
        );
      },
      cell: ({ row }) => {
        const rowData: ConsentPurposeStep = row?.original;
        return (
          <div className="h-full w-full">
            <Trash2
              color="#f00000"
              onClick={() => {
                setShowDeleteModal(true);
                setSelectedRowData(rowData);
              }}
            />
            {/* Delete Consent Purpose Modal */}
          </div>
        );
      },
    },
  ];
  //! STATES
  const [openOptionModal, setOpenOptionModal] = useState(false);
  const [openAddPIIModal, setOpenAddPIIModal] = useState(false);
  const [expirationDate, setExpirationDate] = useState<number | null>(null);
  const [openCreateConsentPurposeModal, setOpenCreateConsentPurposeModal] = useState(false);
  const [consentSelectValue, setConsentSelectValue] = useState<string>('');
  const [processingSelectValue, setProcessingSelectValue] = useState<string | undefined>('');
  const [categorySelectValue, setCategorySelectValue] = useState<string | undefined>('');
  const [consentOptions, setConsentOptions] = useState<
    (ConsentPurposeRecordProperties | NewConsentProperties)[]
  >([]);

  const [purposeOptions, setPurposeOptions] = useState<
    (ProcessingPurposeRecordProperties | NewProcessingPurposeProperties)[]
  >([]);

  const [categoryOptions, setCategoryOptions] = useState<
    (ProcessingCategoryRecordProperties | NewProcessingCategoryProperties)[]
  >([]);
  const [newConsentData, setNewConsentData] = useState<NewConsentProperties | null>();
  const [newProcessingPurposeData, setNewProcessingPurposeData] =
    useState<NewProcessingPurposeProperties | null>();
  const [newProcessingCategoryData, setNewProcessingCategoryData] =
    useState<NewProcessingCategoryProperties | null>();
  const [showDetails, setShowDetails] = useState(false); // New state to control the display of additional details
  const [showSaveButton, setShowSaveButton] = useState(false);
  const [PII, setPII] = useState<PIILabelOptionProperties[]>([]);
  const [consentRecords, setConsentRecords] = useState<ConsentPurposeStep[]>([]);
  const [currentModalTab, setCurrentModalTab] = useState<
    'CONSENT' | 'PROCESSING' | 'CATEGORY' | 'PII'
  >('CONSENT'); // controls which modal content to show
  const [selectedConsentId, setSelectedConsentId] = useState<string | undefined>();
  const [selectedProcessingPurposeId, setSelectedProcessingPurposeId] = useState<
    string | undefined
  >();
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | undefined>();
  const [disablePurpose, setDisablePurpose] = useState<boolean>(false);
  const [disableCategory, setDisableCategory] = useState<boolean>(false);
  const [piiLabelRecords, setPiiLabelRecords] = useState<PIILabelRecordsProperties[]>([]);
  const [piiLabelOptions, setPiiLabelOptions] = useState<PIILabelOptionProperties[]>([]);
  const [frequencyList, setFrequencyList] = useState<ConsentFrequencyProperties[]>([]);
  // New State to manage PII label input for the modal
  const [newPIILabel, setNewPIILabel] = useState<NewPIIProperties>({
    name: '',
    description: '',
  });
  const [makePiiApiCall, setMakePiiApiCall] = useState<boolean>(false);
  const [activeStatus, setActiveStatus] = useState<boolean>(false);
  const [vendorList, setVendorList] = useState<Vendor[]>([]);
  const [processList, setProcessList] = useState<Process[]>([]);
  const [piiMappingData, setPIIMappingData] = useState<Map<number, PIIMapping>>(new Map());
  const [reloadGetConsentRecords, setReloadGetConsentRecords] = useState<boolean>(false);
  const addPIIModalFormData = useForm<AddPIIModalFormData>({
    resolver: zodResolver(AddPIIModalFormSchema),
    defaultValues: {
      pii: '',
      vendor: [],
      process: [],
    } as AddPIIModalFormData,
  }); // Explicitly type defaultValues
  const consentConfigurationForm = useForm<ConsentConfigFormData>({
    resolver: zodResolver(ConsentConfigFormSchema),
    defaultValues: {
      frequency: false,
      frequency_value: '',
      defaultOptOut: false,
      compulsoryConsent: false,
      automaticRevoke: true,
      automaticGrant: true,
      expiry: 0,
    },
  });
  const [isConsentRecordsLoading, setIsConsentRecordsLoading] = useState<boolean>(false);
  const [disableCreateConsentButton, setDisableCreateConsentButton] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [selectedRowData, setSelectedRowData] = useState<ConsentPurposeStep | null>(null);

  const fetchConsentRecords = async () => {
    try {
      // Call the returned function to fetch data
      const responseData = await get_consent_purpose_records(customer_id);
      setConsentOptions(responseData?.result?.data);
    } catch (error) {
      console.error(error);
    }
  };

  // fetching pii label records
  useEffect(() => {
    const fetchPiiData = async () => {
      try {
        const response = await fetchPiiLabelData(customer_id ? customer_id : 0, undefined);
        if (response.status !== 200) {
          throw new Error('Failed to fetch processing purpose data.');
        }

        const piiData = response?.data?.result?.data;

        setPiiLabelRecords(piiData);
        setPiiLabelOptions([
          ...piiData.map((item: PIILabelRecordsProperties) => ({
            label: item.pii_name,
            value: item.id.toString(),
          })),
          { label: 'Other', value: 'other' }, // Adding the "Other" option
        ]);
      } catch (error) {
        if (error instanceof Error) {
          console.error('Fetching processing purpose data failed!');
        } else {
          console.error('An unknown error occurred:', error);
        }
      }
    };

    if (customer_id !== 0) {
      fetchPiiData();
    }
  }, [customer_id, makePiiApiCall]);

  // fetching processing purpose
  useEffect(() => {
    const fetchProcessingPurposeRecords = async () => {
      try {
        // Call the returned function to fetch data
        const responseData = await get_processing_purpose_records(customer_id);
        setPurposeOptions(responseData?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchProcessingPurposeRecords();
  }, [customer_id]);

  // fetching processing category
  useEffect(() => {
    const fetchProcessingPurposeCategoryRecords = async () => {
      try {
        // Call the returned function to fetch data
        const responseData = await get_processing_purpose_category_records(customer_id);
        setCategoryOptions(responseData?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchProcessingPurposeCategoryRecords();
  }, [customer_id]);

  // fetching Step 2 Data
  useEffect(() => {
    setIsConsentRecordsLoading(true);
    const fetchStep2Data = async () => {
      try {
        // Call the API to fetch step 2 data
        const response = await get_collecton_template_step_2(customer_id, collection_template_id);

        // Check if the result and data exist in the response
        if (response?.result?.data) {
          setActiveStatus(response?.result?.data?.active_status);
          setConsentRecords(response?.result?.data?.ct_cp_pl_map);
        }
      } catch (error) {
        console.error('Error fetching step 2 data:', error);
        setConsentRecords([]);
      } finally {
        setIsConsentRecordsLoading(false);
      }
    };

    if (collection_template_id !== -1) fetchStep2Data();
  }, [customer_id, collection_template_id, reloadGetConsentRecords]);

  // fetching frequency
  useEffect(() => {
    const fetchFrequency = async () => {
      try {
        // Call the returned function to fetch data
        const responseData = await get_frequency(customer_id);
        setFrequencyList(responseData?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchFrequency();
  }, [customer_id]);

  //controls the save button
  useEffect(() => {
    if (
      newConsentData ||
      newProcessingPurposeData ||
      (newProcessingCategoryData && categorySelectValue)
    ) {
      setShowSaveButton(true);
    }
  }, [categorySelectValue]);

  // fetching process
  useEffect(() => {
    const fetchProcessList = async () => {
      try {
        // Call the returned function to fetch data
        const responseData = await getProcessList(customer_id);
        setProcessList(responseData?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchProcessList();
  }, [customer_id]);

  // fetching Vendor
  useEffect(() => {
    const fetchVendorList = async () => {
      try {
        // Call the returned function to fetch data
        const responseData = await getVendorList(customer_id);
        setVendorList(responseData?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchVendorList();
  }, [customer_id]);

  // // controls if compulsory consent is true then defaultOptOut is also true
  // useEffect(() => {
  //   if (consentConfigurationForm.getValues('compulsoryConsent')) {
  //     consentConfigurationForm.setValue('defaultOptOut', true);
  //   }
  // }, [consentConfigurationForm.watch('compulsoryConsent')]);

  const renderDialogContent = () => {
    // Select the correct inputData and options array based on currentModalTab
    const inputData =
      currentModalTab === 'CONSENT'
        ? newConsentData
        : currentModalTab === 'PROCESSING'
          ? newProcessingPurposeData
          : currentModalTab === 'CATEGORY'
            ? newProcessingCategoryData
            : newPIILabel;

    const optionsLength =
      currentModalTab === 'CONSENT'
        ? consentOptions.length
        : currentModalTab === 'PROCESSING'
          ? purposeOptions.length
          : currentModalTab === 'CATEGORY'
            ? categoryOptions.length
            : currentModalTab === 'PII'
              ? piiLabelOptions.length
              : 0; // Default to 0 if none of the tabs match

    return (
      <section className="flex flex-col gap-4 font-primary-text">
        <div className="flex flex-col gap-2">
          <Label
            className={`text-left ${
              inputData?.name?.trim() === '' ? 'text-red-500' : 'text-black'
            } text-sm font-medium leading-tight`}
          >
            Name
            <span className="text-red-500">*</span>
          </Label>
          <Input
            value={inputData?.name || ''}
            onChange={(e) => handleModalInputChange('name', e.target.value, optionsLength + 2)}
          />
        </div>
        <div className="flex flex-col gap-2">
          <Label
            className={`text-left ${
              inputData?.description?.trim() === '' ? 'text-red-500' : 'text-black'
            } text-sm font-medium leading-tight`}
          >
            Description<span className="text-red-500">*</span>
          </Label>
          <Textarea
            placeholder="Enter a description"
            value={inputData?.description || ''}
            onChange={(e) =>
              handleModalInputChange('description', e.target.value, optionsLength + 2)
            }
          />
        </div>
        {currentModalTab === 'CONSENT' && (
          <div className="flex flex-col gap-2">
            <Label>
              Expiry<span className="text-red-500">*</span>
            </Label>
            <div className="flex w-full items-center gap-2">
              <Input
                type="number"
                min="0"
                placeholder="Enter expiry"
                className="w-full"
                value={
                  (inputData as NewConsentProperties)?.expiry !== undefined
                    ? (inputData as NewConsentProperties)?.expiry
                    : ''
                }
                onChange={(e) =>
                  handleModalInputChange('expiry', e.target.value, optionsLength + 2)
                }
              />
              <span>Days</span>
            </div>
          </div>
        )}
      </section>
    );
  };
  const renderFooter = () => (
    <footer className="flex w-full flex-row justify-end gap-2 font-primary-text">
      <Button variant="outline" className="text-primary" onClick={handleCloseModal}>
        {t('Common.Cancel')}
      </Button>
      <Button variant="default" className="text-primary-background" onClick={handleModalSave}>
        Save
      </Button>
    </footer>
  );

  //! HANDLER FUNCTIONS

  const handleCloseModal = () => setOpenOptionModal(false);
  const handleOpenOptionModal = () => setOpenOptionModal(true);

  const setStatesToInitial = () => {
    setConsentSelectValue('');
    setProcessingSelectValue('');
    setCategorySelectValue('');
    setNewConsentData(null);
    setNewProcessingPurposeData(null);
    setNewProcessingCategoryData(null);
    setCurrentModalTab('CONSENT');
    setSelectedConsentId(undefined);
    setSelectedProcessingPurposeId(undefined);
    setSelectedCategoryId(undefined);
    setShowSaveButton(false);
    setPIIMappingData(new Map());
    setShowDetails(false);
    setDisableCreateConsentButton(false);
    setExpirationDate(null);
    setDisablePurpose(false);
    setDisableCategory(false);
    addPIIModalFormData.reset({
      pii: '',
      vendor: [],
      process: [],
    });

    consentConfigurationForm.reset({
      frequency: false,
      frequency_value: '',
      defaultOptOut: false,
      compulsoryConsent: false,
      automaticRevoke: true,
      automaticGrant: true,
      expiry: 0,
    });
  };

  const handlePIIOtherOptionModal = () => {
    setCurrentModalTab('PII');
    setOpenOptionModal(true);
  };

  const handleAddPII = (submittedData: z.infer<typeof AddPIIModalFormSchema>) => {
    const pii_id = Number(submittedData?.pii);
    const pii_name: string = piiLabelRecords.find((option) => option.id === pii_id)?.pii_name ?? '';

    setPIIMappingData((prev) => {
      // Clone the existing Map (React state requires immutability)
      const updatedMap = new Map(prev);

      // Update existing entry OR add a new one
      updatedMap.set(pii_id, {
        pii_id: pii_id,
        pii_name: pii_name,
        vendor: submittedData?.vendor,
        process: submittedData?.process,
      });

      return updatedMap; // Update state with new Map
    });

    setOpenAddPIIModal(false);
  };
  const handleNext = async () => {
    toast.dismiss();
    if (consentRecords?.length === 0 || !consentRecords) {
      toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.pleaseSelectConsentPurpose'));
      return;
    }
    // no need to call api again if data is already there from the get api. If new data is added or deleted then needs api call to save
    if (!activeStatus) {
      const requestBody = {
        customer_id: customer_id,
        record_id: collection_template_id,
        subject_identity_type_id: subject_identity_type_id,
        active_status: true,
      };
      try {
        const response = await activate_new_collection_template(requestBody);
      } catch (error) {
        // eslint-disable-next-line unicorn/prefer-ternary
        if (axios.isAxiosError(error)) {
          // Axios specific error handling
          toast.dismiss(); // Clear any existing toasts
          // const status = error?.response?.data?.status_code;
          // const statusText = error?.response?.data?.message;
          const errorMessage = error?.response?.data?.result?.error || error.message;
          toast.error(`${errorMessage}`);
          console.error('Axios Error:', error);
        } else {
          // Generic error handling
          toast.dismiss();
          toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.unexpectedErrorOccurred'));
          console.error('Unexpected Error:', error);
        }
      }
    }

    nextStep();
  };

  const handleModalInputChange = (
    field: 'name' | 'description' | 'expiry',
    value: string,
    id: number | string
  ) => {
    if (currentModalTab === 'CONSENT') {
      setNewConsentData(
        (prevData) =>
          ({
            ...prevData,
            [field]:
              field === 'expiry' ? (value === '' ? undefined : Math.max(0, Number(value))) : value,
            id: `id_${id}`, // Generates a random string ID,
          }) as NewConsentProperties
      );
    } else if (currentModalTab === 'PROCESSING') {
      setNewProcessingPurposeData(
        (prevData) =>
          ({
            ...prevData,
            [field]: value,
          }) as NewProcessingPurposeProperties
      );
    } else if (currentModalTab === 'CATEGORY') {
      setNewProcessingCategoryData(
        (prevData) =>
          ({
            ...prevData,
            [field]: value,
          }) as NewProcessingCategoryProperties
      );
    } else if (currentModalTab === 'PII') {
      setNewPIILabel((prevData) => ({
        ...prevData,
        [field]: value,
      }));
    }
  };

  // Add this function to handle saving values on Save button click
  const handleModalSave = async () => {
    if (currentModalTab === 'CONSENT') {
      if (!newConsentData) {
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.fillAllRequiredFields'));
        return;
      }

      if (newConsentData && !newConsentData.name) {
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.nameRequired'));
        return;
      }

      if (newConsentData && newConsentData.name && newConsentData.name.trim() === '') {
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.nameRequired'));
        return;
      }

      if (newConsentData && !newConsentData.description) {
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.descriptionRequired'));
        return;
      }

      if (
        newConsentData &&
        newConsentData.description &&
        newConsentData.description.trim() === ''
      ) {
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.descriptionRequired'));
        return;
      }

      if (
        newConsentData &&
        (newConsentData.expiry === undefined || newConsentData.expiry === null)
      ) {
        toast.dismiss();
        toast.error('Expiry is required');
        return;
      }

      const newOption = {
        id: `id_${consentOptions?.length + 2}`, // Temporary ID
        name: newConsentData?.name ?? '',
        description: newConsentData?.description ?? '',
        expiry: newConsentData?.expiry,
      };
      setConsentOptions((prevOptions) => [...prevOptions, newOption]);
      setConsentSelectValue(newOption.id?.toString()); // Automatically select the new option
      setCurrentModalTab('PROCESSING'); // Move to the next tab after saving
      setCategorySelectValue('');
      setProcessingSelectValue('');
      setSelectedProcessingPurposeId('');
      setDisablePurpose(false);
      setShowDetails(false);
    } else if (currentModalTab === 'PROCESSING' && newProcessingPurposeData) {
      if (!newProcessingPurposeData.name) {
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.nameRequired'));
        return;
      }

      if (newProcessingPurposeData.name && newProcessingPurposeData.name.trim() === '') {
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.nameRequired'));
        return;
      }

      if (!newProcessingPurposeData.description) {
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.descriptionRequired'));
        return;
      }

      if (
        newProcessingPurposeData.description &&
        newProcessingPurposeData.description.trim() === ''
      ) {
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.descriptionRequired'));
        return;
      }

      const newOption = {
        id: `id_${purposeOptions?.length + 2}`, // Temporary ID
        name: newProcessingPurposeData.name,
        description: newProcessingPurposeData.description,
      };

      setPurposeOptions((prevOptions) => [...prevOptions, newOption]);
      setProcessingSelectValue(newOption.id?.toString()); // Automatically select the new option
      setCurrentModalTab('CATEGORY'); // Move to the next tab after saving
      setDisableCategory(false);
      setShowDetails(false);
    } else if (currentModalTab === 'CATEGORY' && newProcessingCategoryData) {
      const newOption = {
        id: `id_${categoryOptions?.length + 2}`, // Temporary ID
        name: newProcessingCategoryData.name,
        description: newProcessingCategoryData.description,
      };
      setCategoryOptions((prevOptions) => [...prevOptions, newOption]);
      setCategorySelectValue(newOption.id?.toString()); // Automatically select the new option
      setCurrentModalTab('CONSENT'); // Optionally reset or close the modal
      setShowDetails(false);
    } else if (currentModalTab === 'PII' && newPIILabel) {
      setCurrentModalTab('CONSENT');

      const requestBody = {
        customer_id: customer_id,
        pii_label_data: [
          {
            pii_label_description: newPIILabel?.description,
            pii_label_name: newPIILabel?.name,
          },
        ],
      };

      try {
        // Make the API request (replace `yourApiEndpoint` with the actual API endpoint)
        const response = await create_new_pii_label(requestBody);

        if (response.success) {
          toast.dismiss();
          toast.success(convertString(response?.message));
          const newPIIData = response?.result?.data[0];
          setMakePiiApiCall((prev) => !prev);
          // setting modal state to initial
          setNewPIILabel({
            name: '',
            description: '',
          });
          // Add the new PII label and keep "Other" at the end
          // const newOption = {
          //   label: newPIIData?.pii_name,
          //   value: newPIIData?.id?.toString(),
          // };
          // setPiiLabelOptions((prevOptions) => [
          //   ...prevOptions.filter((option) => option.value !== 'other'), // Keep existing items except "Other"
          //   newOption, // Add new option
          //   { label: 'Other', value: 'other' }, // Re-add "Other" at the end
          // ]);
          // setPII((prevPII) => [...prevPII, newOption]); // Automatically select the new PII label
        }
      } catch (error) {
        // eslint-disable-next-line unicorn/prefer-ternary
        if (axios.isAxiosError(error)) {
          // Axios specific error handling
          toast.dismiss(); // Clear any existing toasts
          // const status = error?.response?.data?.status_code;
          // const statusText = error?.response?.data?.message;
          const errorMessage = error?.response?.data?.result?.error || error.message;
          toast.error(`${errorMessage}`);
          console.error('Axios Error:', error);
        } else {
          // Generic error handling
          toast.dismiss();
          toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.unexpectedErrorOccurred'));
          console.error('Unexpected Error:', error);
        }
      }
    }
    handleCloseModal();
  };

  const handleConsentSelectChange = (value: string) => {
    // Open the modal if 'Other' is selected
    if (value === 'create') {
      handleOpenOptionModal();
    } else {
      setDisablePurpose(true);
      setDisableCategory(true);

      setConsentSelectValue(value);
      setSelectedConsentId(value);

      const selectedObject: (NewConsentProperties | ConsentPurposeRecordProperties)[] =
        consentOptions?.filter((option) => option?.id === Number(value));

      // setPurposeOptions([
      //   {
      //     id: selectedObject[0]?.processing_purpose_id?.toString(),
      //     name: selectedObject[0]?.processing_purpose_name?.toString(),
      //   },
      // ]);
      setProcessingSelectValue(selectedObject[0]?.processing_purpose_id?.toString());
      setSelectedProcessingPurposeId(selectedObject[0]?.processing_purpose_id?.toString());

      // setCategoryOptions([
      //   {
      //     id: selectedObject[0]?.processing_purpose_category_id?.toString(),
      //     name: selectedObject[0]?.processing_purpose_category?.toString(),
      //   },
      // ]);
      setCategorySelectValue(selectedObject[0]?.processing_purpose_category_id?.toString());
      setSelectedCategoryId(selectedObject[0]?.processing_purpose_category_id?.toString());

      const selectedConsent = selectedObject[0] as ConsentPurposeRecordProperties;
      if (selectedConsent?.global_expiration_period !== undefined) {
        setExpirationDate(selectedConsent?.global_expiration_period);
        consentConfigurationForm.setValue('expiry', selectedConsent.global_expiration_period);
      }
    }
  };

  const handleProcessingSelectChange = (value: string) => {
    // Open the modal if 'Other' is selected
    if (value === 'create') {
      handleOpenOptionModal();
    } else {
      setDisableCategory(true);

      setSelectedProcessingPurposeId(value);
      setProcessingSelectValue(value);

      const selectedObject: (NewProcessingPurposeProperties | ProcessingPurposeRecordProperties)[] =
        purposeOptions?.filter((option) => option?.id === Number(value));

      // setCategoryOptions([
      //   {
      //     id: selectedObject[0]?.category_id?.toString(),
      //     name: selectedObject[0]?.category_name?.toString(),
      //   },
      // ]);
      setCategorySelectValue(selectedObject[0]?.category_id?.toString());
      setSelectedCategoryId(selectedObject[0]?.category_id?.toString());
    }
  };

  const handleCategorySelectChange = (value: string) => {
    // Open the modal if 'Other' is selected
    if (value === 'create') {
      handleOpenOptionModal();
    } else {
      setSelectedCategoryId(value);
      setCategorySelectValue(value);
    }
  };

  // Add this function to handle saving the new created item
  const handleSaveCreatedItem = async () => {
    const newData: CreateNewConsentPurposeProperties = {
      customer_id: customer_id, // MANDATORY
      collection_template_id: collection_template_id, // MANDATORY
      cp_id: Number(selectedConsentId), //OPTIONAL BELOW ALL
      cp_name: selectedConsentId ? null : newConsentData?.name,
      cp_description: selectedConsentId ? null : newConsentData?.description,
      cp_global_expiration_period: selectedConsentId ? null : newConsentData?.expiry,
      pp_id: selectedProcessingPurposeId ? Number(selectedProcessingPurposeId) : null,
      pp_name: selectedProcessingPurposeId ? null : newProcessingPurposeData?.name,
      pp_description: selectedProcessingPurposeId ? null : newProcessingPurposeData?.description,
      ppc_id: selectedCategoryId ? Number(selectedCategoryId) : null,
      ppc_name: selectedCategoryId ? null : newProcessingCategoryData?.name,
      ppc_description: selectedCategoryId ? null : newProcessingCategoryData?.description,
    };

    try {
      // Send the data to the API and await the response
      const response = await create_new_consent_purpose(newData);
      if (response?.success) {
        toast.dismiss();
        toast.success(response?.message);

        setSelectedConsentId(response?.result?.data?.[0]?.id);
        setSelectedProcessingPurposeId(response?.result?.data?.[0]?.processing_purpose_id);
        setSelectedCategoryId(response?.result?.data?.[0]?.processing_purpose_category_id);
        setExpirationDate(response?.result?.data?.[0]?.global_expiration_period);
        setDisableCategory(true);
        setDisablePurpose(true);
        setShowSaveButton(false);
        setShowDetails(true); // Show additional details after save is successful
        setNewConsentData(null);
        setNewProcessingPurposeData(null);
        setNewProcessingCategoryData(null);
      }
    } catch (error) {
      // eslint-disable-next-line unicorn/prefer-ternary
      if (axios.isAxiosError(error)) {
        // Axios specific error handling
        toast.dismiss(); // Clear any existing toasts
        // const status = error?.response?.data?.status_code;
        // const statusText = error?.response?.data?.message;
        const errorMessage = error?.response?.data?.result?.error || error.message;
        toast.error(`${errorMessage}`);
        console.error('Axios Error:', error);
      } else {
        // Generic error handling
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.unexpectedErrorOccurred'));
        console.error('Unexpected Error:', error);
      }
    }

    if (!selectedProcessingPurposeId) {
      // logic for getting data from purpose modal
    }

    if (!selectedCategoryId) {
      // logic for getting data from category modal
    }
  };

  const handleSaveConsentPurpose = async (
    submittedData: z.infer<typeof ConsentConfigFormSchema>
  ) => {
    function sanitizeObject(obj: Record<string, any>): Record<string, any> {
      return Object.fromEntries(
        Object.entries(obj).filter(([_, v]) => v !== undefined && v !== null)
      );
    }

    const requestBody = sanitizeObject({
      customer_id: customer_id || 0,
      collection_template_id: collection_template_id || 0,
      pii_label_id_list: Array.from(piiMappingData.keys()),
      ct_cp_map: Array.from(piiMappingData.values()).map((item) => {
        return sanitizeObject({
          consent_purpose_id: Number(selectedConsentId),
          processing_purpose_id: Number(selectedProcessingPurposeId),
          pii_label_id: item?.pii_id,
          frequency: consentConfigurationForm?.getValues('frequency')
            ? consentConfigurationForm?.getValues('frequency_value')?.toLowerCase()
            : null,
          consent_lifetime: consentConfigurationForm?.getValues('expiry'),
          default_opt_out: consentConfigurationForm?.getValues('defaultOptOut'),
          compulsory_consent: consentConfigurationForm?.getValues('compulsoryConsent'),
          manual_grant_bool: !consentConfigurationForm?.getValues('automaticGrant'),
          manual_revoke_bool: !consentConfigurationForm?.getValues('automaticRevoke'),
          vendor_ids: item?.vendor?.length ? item.vendor.map((vendor) => vendor.id) : undefined,
          process_ids: item?.process?.length
            ? item.process.map((process) => process.id)
            : undefined,
        });
      }),
    });

    try {
      const response = await create_consent_purpose_mapping(requestBody);
      if (response.success) {
        const requestBody = {
          customer_id: customer_id,
          record_id: collection_template_id,
          subject_identity_type_id: subject_identity_type_id,
          active_status: true,
        };
        if (!activeStatus) {
          const response = await activate_new_collection_template(requestBody);
        }
        toast.dismiss();
        toast.success(convertString(response?.message));
        setOpenCreateConsentPurposeModal(false);
        setReloadGetConsentRecords((prev) => !prev);
        setDisableCreateConsentButton(true);
      }
    } catch (error) {
      // eslint-disable-next-line unicorn/prefer-ternary
      if (axios.isAxiosError(error)) {
        // Axios specific error handling
        toast.dismiss(); // Clear any existing toasts
        // const status = error?.response?.data?.status_code;
        // const statusText = error?.response?.data?.message;
        const errorMessage = error?.response?.data?.result?.error || error.message;
        toast.error(`${errorMessage}`);
        console.error('Axios Error:', error);
      } else {
        // Generic error handling
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.unexpectedErrorOccurred'));
        console.error('Unexpected Error:', error);
      }
    }
  };

  const handleDeleteConsentPurpose = async (data: ConsentPurposeStep | null) => {
    const record_ids = data?.pii_map
      ?.flatMap((item) => item?.ct_cp_map?.map((consent) => consent?.id))
      ?.filter((id) => id !== undefined && id !== null); // Ensure no undefined or null values

    const requestBody = {
      customer_id: customer_id,
      collection_template_id: collection_template_id,
      record_ids: record_ids,
    };

    try {
      // Send the data to the API and await the response
      const response = await delete_consent_purpose(requestBody);
      if (response?.success) {
        toast.dismiss();
        toast.success(response?.message);
        setShowDeleteModal(false);
        setReloadGetConsentRecords((prev) => !prev);
      }
    } catch (error) {
      // eslint-disable-next-line unicorn/prefer-ternary
      if (axios.isAxiosError(error)) {
        // Axios specific error handling
        toast.dismiss(); // Clear any existing toasts
        // const status = error?.response?.data?.status_code;
        // const statusText = error?.response?.data?.message;
        const errorMessage = error?.response?.data?.result?.error || error.message;
        toast.error(`${errorMessage}`);
        console.error('Axios Error:', error);
      } else {
        // Generic error handling
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.unexpectedErrorOccurred'));
        console.error('Unexpected Error:', error);
      }
    }
  };

  const handleConsentConfigurationSubmit = (data: ConsentConfigFormData) => {};

  useEffect(() => {
    if (consentSelectValue && processingSelectValue && categorySelectValue) {
      setShowDetails(true);
    }
  }, [consentSelectValue, processingSelectValue, categorySelectValue]);

  return (
    <section>
      <StepperFormActions handleClick={handleNext} />
      <ShadcnDialog
        open={showDeleteModal}
        onOpenChange={setShowDeleteModal}
        title={`Delete ${selectedRowData?.consent_purpose_name}`}
        footer={
          <div className="flex w-full flex-row justify-end gap-2 font-primary-text">
            <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
              No
            </Button>
            <Button
              className="text-primary-background"
              onClick={() => handleDeleteConsentPurpose(selectedRowData)}
            >
              Yes
            </Button>
          </div>
        }
        dialogContentClassName="h-auto max-h-[80vh] overflow-y-auto sm:max-w-[50vh]"
      >
        <p className="font-primary-text">{`Are you sure you want to delete ${selectedRowData?.consent_purpose_name}?`}</p>
        <div className="flex items-start gap-3 rounded-md border-l-4 border-yellow-500 bg-yellow-50 p-4 shadow-sm">
          <AlertTriangle className="mt-1 text-yellow-600" />
          <div>
            <p className="font-medium text-yellow-800">
              You are about to remove a consent purpose.
            </p>
            <p className="text-sm text-yellow-700">
              Please review the updated consent form to ensure accuracy and compliance.
            </p>
          </div>
        </div>
      </ShadcnDialog>
      {/* Add Consent Purpose */}
      <div
        className="mt-2.5 flex w-full flex-col gap-2 overflow-auto rounded-lg border border-primary-border bg-primary-background font-primary-text"
        style={{ height: 'calc(100vh - 340px)' }}
      >
        <div className="flex h-full flex-col justify-between">
          {consentRecords?.length > 0 && (
            <div className="flex h-fit w-full flex-col gap-2 p-4">
              <p>Consent Purpose</p>
              <DynamicTable<ConsentPurposeStep>
                data={consentRecords} // Convert Map to an array
                loading={isConsentRecordsLoading}
                columns={consentRecordsColumns}
                enableSorting
                collapsible
                collapsibleColumnName="PII Details"
                renderExpandedContent={(rowData) => (
                  <div className="h-full w-full">
                    <DynamicTable<PIIMap>
                      data={rowData?.pii_map} // Convert Map to an array
                      loading={isConsentRecordsLoading}
                      columns={consentRecordsPIIColumn}
                      enableSorting
                    />
                  </div>
                )}
              />
            </div>
          )}
          <div className="flex w-full justify-end p-4">
            <Button
              className="text-primary-background"
              onClick={() => {
                setOpenCreateConsentPurposeModal(true);
                setStatesToInitial();
                fetchConsentRecords()
              }}
            >
              Add Consent Purpose
            </Button>
          </div>
          <div className="flex w-full flex-row gap-2 pb-4 pl-4">
            <p>
              Do you want to include
              <i>
                <strong> data retention </strong>
              </i>
              policy in this template?
            </p>
            <RadioGroup
              defaultValue={savedDataRetention ? 'yes' : 'no'}
              className="flex flex-row gap-2"
              onValueChange={(value) => {
                if (value === 'yes') {
                  dispatch(universalConsentManagementActions.setDataRetention(true));
                } else {
                  dispatch(universalConsentManagementActions.setDataRetention(false));
                }
              }}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="1" />
                <Label htmlFor="r2">Yes</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="0" />
                <Label htmlFor="r3">No</Label>
              </div>
            </RadioGroup>
          </div>
        </div>
      </div>

      {/* Add Consent Purpose Modal */}
      <ShadcnDialog
        open={openCreateConsentPurposeModal}
        onOpenChange={setOpenCreateConsentPurposeModal}
        title="Add Consent Purpose"
        footer={
          <div className="flex w-full justify-end font-primary-text">
            <Button
              className="text-primary-background"
              onClick={consentConfigurationForm.handleSubmit(handleSaveConsentPurpose)}
              disabled={disableCreateConsentButton}
            >
              Save
            </Button>
          </div>
        }
        dialogContentClassName="h-auto max-h-[80vh] overflow-y-auto sm:max-w-[80vh]"
      >
        <div className="h-full w-full overflow-auto bg-primary-background font-primary-text">
          <div className={`flex h-full flex-col gap-4 p-4 ${styles.table_main_content}`}>
            <div className="flex w-full flex-col gap-2">
              <Label>Consent Purpose:</Label>
              <Select
                value={consentSelectValue}
                onValueChange={(value: string) => handleConsentSelectChange(value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select a consent purpose" />
                </SelectTrigger>
                <SelectContent className="flex flex-col">
                  <SelectGroup className="flex flex-col">
                    <SelectLabel>Consent Purpose</SelectLabel>
                    {consentOptions?.map((option) => (
                      <TooltipProvider key={option?.id?.toString()}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <SelectItem className="block" value={option?.id?.toString()}>
                              {option?.name}
                            </SelectItem>
                          </TooltipTrigger>
                          <TooltipContent className="font-primary-text text-primary-background">
                            {`Processing Purpose: ${option?.processing_purpose_name}, Processing Category: ${option?.processing_purpose_category}`}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ))}
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <SelectItem className="block" value="create">
                            Create
                          </SelectItem>
                        </TooltipTrigger>
                        <TooltipContent className="font-primary-text text-primary-background">
                          Create a new consent purpose
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            {consentSelectValue && (
              <div className="flex w-full flex-row gap-2">
                <div className="flex w-[50%] flex-col gap-2">
                  <Label>Processing Purpose:</Label>
                  <Select
                    value={processingSelectValue}
                    onValueChange={(value: string) => handleProcessingSelectChange(value)}
                    disabled={disablePurpose}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a processing purpose" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>Processing Purpose</SelectLabel>
                        {purposeOptions?.map((option) => {
                          return (
                            <SelectItem value={option?.id?.toString()}>{option?.name}</SelectItem>
                          );
                        })}
                        <SelectItem value="create">Create</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>
                {processingSelectValue && (
                  <div className="flex w-[50%] flex-col gap-2">
                    <Label>Processing Category:</Label>
                    <Select
                      value={categorySelectValue}
                      onValueChange={(value: string) => handleCategorySelectChange(value)}
                      disabled={disableCategory}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select a processing category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectLabel>Processing Category</SelectLabel>
                          {categoryOptions?.map((option) => {
                            return (
                              <SelectItem value={option?.id?.toString()}>{option?.name}</SelectItem>
                            );
                          })}
                          <SelectItem value="create">Create</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
            )}

            {/* Render the Save button if created new consent */}
            {showSaveButton && (
              <div className="flex w-full justify-end">
                <Button className="text-primary-background" onClick={handleSaveCreatedItem}>
                  Save
                </Button>
              </div>
            )}

            {/* PII Mapping Table */}
            {piiMappingData.size > 0 && (
              <div className="flex h-fit w-full flex-col gap-2">
                <p className="font-sm text-sm">PII Table</p>
                <DynamicTable<PIIMapping>
                  data={[...piiMappingData.values()]} // Convert Map to an array
                  loading={false}
                  columns={columns}
                  enableSorting
                />
              </div>
            )}

            {/* Show PII, frequency, expiry fields only if showDetails is true */}
            {!showSaveButton && showDetails && (
              <div className="flex w-full justify-end">
                <Button
                  className="text-primary-background"
                  onClick={() => {
                    setOpenAddPIIModal(true);
                    addPIIModalFormData.reset();
                    disableADDPII.current = false;
                  }}
                >
                  <img src={plusSign} alt="add sign" />
                  <p>Add {`${piiMappingData?.size > 0 ? 'More PII' : 'PII'}`}</p>
                </Button>
              </div>
            )}

            {/* Consent Configuration */}
            {piiMappingData.size > 0 && (
              <div className="flex h-fit w-full flex-col gap-2">
                <p className="font-sm text-sm">Consent Configuration</p>
                <Form {...consentConfigurationForm}>
                  <form
                    onSubmit={consentConfigurationForm.handleSubmit(
                      handleConsentConfigurationSubmit
                    )}
                    className="flex h-auto w-full flex-row flex-wrap items-center gap-4 rounded-lg bg-grey-light p-4"
                  >
                    {/* Frequency Switch */}
                    <FormField
                      control={consentConfigurationForm.control}
                      name="frequency"
                      render={({ field }) => (
                        <FormItem className="flex w-fit flex-row items-center space-x-3">
                          <FormLabel>Frequency</FormLabel>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {consentConfigurationForm?.getValues('frequency') && (
                      <FormField
                        control={consentConfigurationForm.control}
                        name="frequency_value"
                        render={({ field }) => (
                          <FormItem className="mt-2 flex w-[200px] flex-row items-center space-x-3 space-y-2">
                            {/* <FormLabel className="flex w-full flex-row items-center gap-2">
                            <p>Frequency*</p>
                          </FormLabel> */}
                            <Select
                              {...field}
                              value={consentConfigurationForm
                                ?.getValues('frequency_value')
                                ?.toString()}
                              onValueChange={(value) => field.onChange(value)}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select Frequency" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectGroup>
                                  <SelectLabel>Frequency</SelectLabel>

                                  {frequencyList?.map((frequency: ConsentFrequencyProperties) => {
                                    return (
                                      <SelectItem
                                        value={String(frequency?.key)}
                                        key={frequency?.key}
                                      >
                                        {frequency?.name}
                                      </SelectItem>
                                    );
                                  })}
                                </SelectGroup>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Default Opt Out Switch */}
                    <FormField
                      control={consentConfigurationForm.control}
                      name="defaultOptOut"
                      render={({ field }) => (
                        <FormItem className="flex w-fit flex-row items-center space-x-3">
                          <FormLabel>Default Opt Out</FormLabel>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              disabled={consentConfigurationForm?.watch('compulsoryConsent')}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    {/* Compulsory Consent Switch */}
                    <FormField
                      control={consentConfigurationForm.control}
                      name="compulsoryConsent"
                      render={({ field }) => (
                        <FormItem className="flex w-fit flex-row items-center space-x-3">
                          <FormLabel>Compulsory Consent</FormLabel>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={() => {
                                consentConfigurationForm.setValue(
                                  'compulsoryConsent',
                                  !field.value
                                );
                                consentConfigurationForm.setValue('defaultOptOut', !field.value);
                              }}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    {/* Automatic Revoke Switch */}
                    <FormField
                      control={consentConfigurationForm.control}
                      name="automaticRevoke"
                      render={({ field }) => (
                        <FormItem className="flex w-fit flex-row items-center space-x-3">
                          <FormLabel>Automatic Revoke</FormLabel>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    {/* Automatic Grant Switch */}
                    <FormField
                      control={consentConfigurationForm.control}
                      name="automaticGrant"
                      render={({ field }) => (
                        <FormItem className="flex w-fit flex-row items-center space-x-3">
                          <FormLabel>Automatic Grant</FormLabel>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    {/* Expiry Input Field */}

                    <FormField
                      control={consentConfigurationForm.control}
                      name="expiry"
                      render={({ field }) => (
                        <FormItem className="flex w-[150px] flex-row items-center space-x-3">
                          <FormLabel>Expiry*</FormLabel>
                          <div className="flex w-full flex-col gap-2">
                            <FormControl>
                              <Input
                                type="number"
                                disabled={expirationDate ? true : false}
                                {...field}
                                value={expirationDate ? expirationDate : field.value}
                              />
                            </FormControl>
                            <FormMessage />
                          </div>
                        </FormItem>
                      )}
                    />
                  </form>
                  {/* Submit Button */}
                  {/* <Button type="submit" className="text-primary-background">
                  Submit
                </Button> */}
                </Form>
                <ConsentWarning
                  formId={form_id}
                  preferenceCenterId={preference_center_id}
                  languages={languages}
                />
              </div>
            )}
          </div>
        </div>
      </ShadcnDialog>
      <ShadcnDialog
        open={openOptionModal}
        onOpenChange={handleCloseModal}
        title={
          currentModalTab === 'CONSENT'
            ? 'Create Consent Purpose'
            : currentModalTab === 'PROCESSING'
              ? 'Create Processing Purpose'
              : currentModalTab === 'CATEGORY'
                ? 'Create Processing Category'
                : currentModalTab === 'PII'
                  ? 'Create PII Label'
                  : ''
        }
        footer={renderFooter()}
        dialogContentClassName="h-auto max-h-[80vh] overflow-y-auto sm:max-w-[50%]"
      >
        {renderDialogContent()}
      </ShadcnDialog>

      {/* Add PII Modal */}
      <ShadcnDialog
        open={openAddPIIModal}
        onOpenChange={setOpenAddPIIModal}
        title="Add PII"
        footer={
          <div className="flex w-full justify-end font-poppins">
            <Button
              className="text-primary-background"
              onClick={addPIIModalFormData.handleSubmit(handleAddPII)}
            >
              Save
            </Button>
          </div>
        }
        dialogContentClassName="h-auto max-h-[80vh] overflow-y-auto sm:max-w-[450px]"
      >
        <Form {...addPIIModalFormData}>
          <form
            // onSubmit={addPIIModalFormData.handleSubmit(handleAddPII)}
            className="flex h-full w-full flex-col items-center gap-4 font-primary-text"
          >
            {/* PII Drowpdown */}
            <FormField
              control={addPIIModalFormData.control}
              name="pii"
              disabled={disableADDPII.current}
              render={({ field }) => (
                <FormItem className="w-full min-w-[280px]">
                  <FormLabel className="flex w-full flex-row items-center gap-2">
                    <p>PII</p>
                    {/* <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <img src={infoIcon} alt="info" className="aspect-square h-4 w-4" />
                              </TooltipTrigger>
                              <TooltipContent className="text-base text-primary-background">
                                Personal information that is collected to identify the end user.
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider> */}
                  </FormLabel>
                  <Select
                    {...field}
                    value={addPIIModalFormData?.getValues('pii')?.toString()}
                    onValueChange={(value) => field.onChange(value)}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select PII" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="-1" disabled>
                        {t('Common.Select')}
                      </SelectItem>
                      {piiLabelRecords?.map((pii: PIILabelRecordsProperties) => {
                        return (
                          <SelectItem value={String(pii?.id)} key={pii?.id}>
                            {pii?.pii_name}
                          </SelectItem>
                        );
                      })}
                      {/* <SelectItem value="create">Create PII</SelectItem> */}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Vendor Drowpdown */}
            <FormField
              control={addPIIModalFormData.control}
              name="vendor"
              render={({ field }) => (
                <FormItem className="w-full min-w-[280px]">
                  <FormLabel>Vendor</FormLabel>
                  <MultipleSelector
                    options={vendorList.map((vendor) => ({
                      value: vendor.id.toString(),
                      label: vendor.name,
                    }))}
                    placeholder="Select Vendor"
                    badgeClassName="custom-badge-class text-white"
                    handleOpenOptionModal={() => {
                      // Handle modal logic here if required
                    }}
                    onChange={(selectedOptions) => {
                      // Convert selected options to `{ id, name }` format
                      const newSelectedVendors = selectedOptions.map((option) => ({
                        id: Number(option.value),
                        name: option.label,
                      }));

                      field.onChange(newSelectedVendors);
                    }}
                    value={addPIIModalFormData
                      ?.getValues('vendor')
                      ?.map((vendor: { id: number; name: string }) => ({
                        value: vendor.id.toString(),
                        label: vendor.name,
                      }))}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Process Drowpdown */}
            <FormField
              control={addPIIModalFormData.control}
              name="process"
              render={({ field }) => (
                <FormItem className="w-full min-w-[280px]">
                  <FormLabel className="flex w-full flex-row items-center gap-2">
                    <p>Process</p>
                  </FormLabel>
                  <MultipleSelector
                    options={processList.map((process) => ({
                      value: process.id.toString(),
                      label: process.name,
                    }))}
                    placeholder="Select Process"
                    badgeClassName="custom-badge-class text-white"
                    handleOpenOptionModal={() => {
                      // Handle modal logic here if required
                    }}
                    onChange={(selectedOptions) => {
                      // Convert selected options to `{ id, name }` format
                      const selectedProcess = selectedOptions.map((option) => ({
                        id: Number(option.value),
                        name: option.label,
                      }));

                      field.onChange(selectedProcess);
                    }}
                    value={addPIIModalFormData
                      ?.getValues('process')
                      ?.map((process: { id: number; name: string }) => ({
                        value: process.id.toString(),
                        label: process.name,
                      }))}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </ShadcnDialog>

      {/* <AddOptionDailog
        openModal={openOptionModal}
        closeModal={handleCloseModal}
        label="Add Option"
        title="Name*"
      /> */}
    </section>
  );
}

export default AddPIILabelStepTwo;
