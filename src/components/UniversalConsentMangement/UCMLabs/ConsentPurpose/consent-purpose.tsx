import { ColumnDef } from '@tanstack/react-table';
import { <PERSON><PERSON><PERSON><PERSON>own, <PERSON>cil, Trash } from 'lucide-react';
import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/Button';
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuTrigger,
// } from '../../../../@/components/ui/Common/Elements/Select/DropDownMenu';
import { useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../../../../@/components/ui/AlertDialog';
import CommonConfirmationDialog from '../../../../@/components/ui/Common/Elements/Dialog/CommonConfirmationDialog';
import { Input } from '../../../../@/components/ui/Common/Elements/Input/Input';
import { Label } from '../../../../@/components/ui/Common/Elements/Label/Label';
import { Textarea } from '../../../../@/components/ui/textarea';
import httpClient from '../../../../api/httpClient';
import { RootState } from '../../../../redux/store';
import {
  AddProcessingPurposeData,
  ConsentPurposeData,
  IDeleteComponent,
  ProcessingPurposeProperties,
  UpdateConsentPurposeData,
} from '../../../../types/universal-consent-management';
import {
  DELETE_UCM_LAB_COMPONENT,
  FETCH_UCM_CONSENT_PURPOSE_LIST,
  UPDATE_UCM_CONSENT_PURPOSE,
} from '../../../common/api';
import {
  fetchConsentPurposeData,
  updateUcmLabData,
} from '../../../common/services/universal-consent-management';
import ShadcnDialog from '../../../common/shadcn-dialog';
import DynamicTable from '../../../common/ShadcnDynamicTable/dynamic-table';
import { GradientSparkles } from '../../Common/GradientSparkles';
import ProcessingPurposeCategoryCell from './add-processing-purpose-category';

const handleDataChange = <T extends Record<string, string | number>>(
  event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  setData: React.Dispatch<React.SetStateAction<T>>
) => {
  const { name, value, type } = event.target;
  console.log(value, 'value123');

  setData((prevData) => ({
    ...prevData,
    [name]: value,
  }));
};

const ConsentPurpose: React.FC<ProcessingPurposeProperties> = ({ reloadLabList, searchValue }) => {
  const loginData = useSelector((state: RootState) => state.auth.login.login_details);

  let customer_id = 0;

  if (loginData !== null) {
    customer_id = loginData.customer_id ?? 0;
  }

  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isAlertDialogOpen, setIsAlertDialogOpen] = useState(false);

  const [reloadConsentPurpose, setReloadConsentPurpose] = useState(true);
  const [selectedPurposeId, setSelectedPurposeId] = useState<number | null>(null);

  const [consentPurposeData, setConsentPurposeData] = useState<UpdateConsentPurposeData>({
    consent_purpose_id: -1,
    name: '',
    description: '',
    global_expiration_period: '',
  });
  // console.log({ consentPurposeList });
  // const data: ConsentPurposeData[] = [
  //   {
  //     id: 1,
  //     name: 'Essential Cookies',
  //     description:
  //       'Cookies necessary for the website to function properly and cannot be switched off in our systems.',
  //     processing_purpose: {
  //       id: 1,
  //       name: 'Functional'
  //     }
  //   },
  //   {
  //     id: 2,
  //     name: 'Performance Cookies',
  //     description:
  //       'Cookies that allow us to count visits and traffic sources so we can measure and improve the performance of our site.',
  //     processing_purpose: {
  //       id: 2,
  //       name: 'Analytics'
  //     }
  //   },
  //   {
  //     id: 3,
  //     name: 'Functional Cookies',
  //     description:
  //       'Cookies that enable the website to provide enhanced functionality and personalization.',
  //     processing_purpose: {
  //       id: 3,
  //       name: 'Preferences'
  //     }
  //   },
  //   {
  //     id: 4,
  //     name: 'Targeting Cookies',
  //     description:
  //       'Cookies set by our advertising partners to profile your interests and show relevant ads on other sites.',
  //     processing_purpose:  {
  //       id: 4,
  //       name: 'Advertising'
  //     }
  //   },
  //   {
  //     id: 5,
  //     name: 'Social Media Cookies',
  //     description: 'Cookies that enable you to share our content through social media platforms.',
  //     processing_purpose: {
  //       id: 5,
  //       name: 'Social Media',
  //     }
  //   },
  //   {
  //     id: 6,
  //     name: 'Email Marketing',
  //     description: 'Using your email address to send you newsletters, offers, and updates.',
  //     processing_purpose: {
  //       id: 6,
  //       name: 'Email'
  //     }
  //   },
  //   {
  //     id: 7,
  //     name: 'SMS Notifications',
  //     description: 'Sending text messages to your mobile device with promotions and updates.',
  //     processing_purpose: {
  //       id: 7,
  //       name: 'SMS'
  //     }
  //   },
  //   {
  //     id: 8,
  //     name: 'Push Notifications',
  //     description: 'Delivering real-time updates and alerts directly to your device or browser.',
  //     processing_purpose: {
  //       id: 8,
  //       name: 'Notifications'
  //     }
  //   },
  //   {
  //     id: 9,
  //     name: 'Location Tracking',
  //     description:
  //       'Collecting your geographic location to provide location-based services and content.',
  //     processing_purpose: {
  //       id: 9,
  //       name: 'Location'
  //     }
  //   },
  //   {
  //     id: 10,
  //     name: 'Third-Party Data Sharing',
  //     description:
  //       'Sharing your data with third-party partners for joint marketing and service enhancement.',
  //     processing_purpose: {
  //       id: 10,
  //       name: 'Data Sharing'
  //     }
  //   },
  //   {
  //     id: 11,
  //     name: 'Behavioral Advertising',
  //     description:
  //       'Using data collected from your browsing behavior to display personalized advertisements.',
  //     processing_purpose: {
  //       id: 11,
  //       name: 'Advertising'
  //     }
  //   },
  //   {
  //     id: 12,
  //     name: 'Customer Profiling',
  //     description:
  //       'Analyzing your preferences and behavior to create a profile for personalized services.',
  //     processing_purpose: {
  //       id: 12,
  //       name: 'Analytics'
  //     }
  //   },
  //   {
  //     id: 13,
  //     name: 'Data Analytics',
  //     description: 'Aggregating user data to improve our products and services.',
  //     processing_purpose: {
  //       id: 13,
  //       name: 'Analytics'
  //     }
  //   },
  //   {
  //     id: 14,
  //     name: 'User Surveys',
  //     description: 'Collecting feedback through surveys to enhance user experience.',
  //     processing_purpose: {
  //       id: 14,
  //       name: 'Feedback'
  //     }
  //   },
  //   {
  //     id: 15,
  //     name: 'A/B Testing',
  //     description:
  //       'Testing different versions of our site to optimize performance and user experience.',
  //     processing_purpose: {
  //       id: 15,
  //       name:  'Optimization'
  //     }
  //   },
  //   {
  //     id: 16,
  //     name: 'Content Personalization',
  //     description: 'Customizing content based on your interests and browsing history.',
  //     processing_purpose: {
  //       id: 16,
  //       name: 'Content'
  //     }
  //   },
  //   {
  //     id: 17,
  //     name: 'Newsletter Subscription',
  //     description: 'Sending you regular updates and news about our services.',
  //     processing_purpose:{
  //       id: 17,
  //       name:  'Subscription'
  //     }
  //   },
  //   {
  //     id: 18,
  //     name: 'Product Recommendations',
  //     description:
  //       'Suggesting products or services that might interest you based on your activity.',
  //     processing_purpose: {
  //       id: 18,
  //       name: 'Recommendations'
  //     }
  //   },
  //   {
  //     id: 19,
  //     name: 'Social Media Integration',
  //     description: 'Connecting your account with social media platforms for a seamless experience.',
  //     processing_purpose: {
  //       id: 19,
  //       name: 'Integration'
  //     }
  //   },
  //   {
  //     id: 20,
  //     name: 'Video Playback Preferences',
  //     description:
  //       'Storing your preferences for video playback to enhance your viewing experience.',
  //     processing_purpose: {
  //       id: 20,
  //       name: 'Functional'
  //     }
  //   },
  //   {
  //     id: 21,
  //     name: 'Error Reporting',
  //     description: 'Collecting error data to diagnose and fix technical issues.',
  //     processing_purpose: {
  //       id: 21,
  //       name: 'Diagnostics'
  //     }
  //   },
  //   {
  //     id: 22,
  //     name: 'Affiliate Marketing',
  //     description: 'Tracking referrals and affiliate links for commission purposes.',
  //     processing_purpose: {
  //       id: 22,
  //       name: 'Affiliate'
  //     }
  //   },
  //   {
  //     id: 23,
  //     name: 'Live Chat Support',
  //     description: 'Providing real-time assistance through live chat services.',
  //     processing_purpose: {
  //       id: 23,
  //       name: 'Support'
  //     }
  //   },
  //   {
  //     id: 24,
  //     name: 'Event Tracking',
  //     description: 'Monitoring user interactions to understand engagement with our content.',
  //     processing_purpose: {
  //       id: 24,
  //       name: 'Tracking'
  //     }
  //   },
  //   {
  //     id: 25,
  //     name: 'Data Storage Consent',
  //     description: 'Securely storing your data for account management and service provision.',
  //     processing_purpose: {
  //       id: 25,
  //       name: 'Storage'
  //     }
  //   },
  // ];
  const { data: singleConsentPurposeData } = useQuery({
    queryKey: ['consent_purpose_single', selectedPurposeId],
    queryFn: async () => {
      const response = await httpClient.get(
        `${FETCH_UCM_CONSENT_PURPOSE_LIST}?customer_id=${customer_id}&consent_purpose_id=${selectedPurposeId}&warning=true`
      );
      return response?.data?.result?.data;
    },
    enabled: !!selectedPurposeId && isDialogOpen,
  });
  const handleEdit = (purpose: ConsentPurposeData) => {
    setSelectedPurposeId(purpose.id);
    console.log({ singleConsentPurposeData });

    setIsDialogOpen(true);
    setConsentPurposeData({
      consent_purpose_id: purpose?.id,
      name: purpose?.name,
      description: purpose?.description,
      global_expiration_period: purpose?.global_expiration_period,
    });
  };

  const handleOpenChange = (value: boolean) => {
    setIsDialogOpen(value);

    // Resetting to default values on closing
    if (!value) {
      setIsSubmitted(false);
      setConsentPurposeData({
        consent_purpose_id: -1,
        name: '',
        description: '',
        global_expiration_period: 0,
      });
    }
  };

  const handleSubmitData = async <T extends Record<string, string | number | number[] | null>>(
    path: string,
    data: T
  ) => {
    console.log(data, 'data123');
    if (customer_id === 0) return;

    const globalExpiration = Number(data.global_expiration_period);

    if (!globalExpiration) {
      toast.error('Expiry period is required and cannot be 0');
      return;
    }

    if (globalExpiration > 12000) {
      toast.error('Expiry period cannot exceed 12,000 days.');
      return;
    }

    if (singleConsentPurposeData?.alert === true && !isAlertDialogOpen) {
      setIsAlertDialogOpen(true);
      return;
    }

    try {
      const response = await updateUcmLabData(path, data);
      if (response.status === 200) {
        handleOpenChange(false);
        setReloadConsentPurpose(!reloadConsentPurpose);

        toast.dismiss();
        toast.success(t('ToastMessages.Consent.ConsentPurposeUpdatedSuccessfully'));
      }
    } catch (error: unknown) {
      console.error('Error:', error);
      toast.dismiss();

      if (axios.isAxiosError(error)) {
        toast.error(error.response?.data?.message);
      } else {
        toast.error(`Couldn't update consent purpose`);
      }
    }
  };

  // const handleDelete = (id: number) => {
  //   // Handle delete action
  // };

  const { mutate: handleDeleteLabComponent } = useMutation({
    mutationFn: async (data: IDeleteComponent) => {
      const { tab, id } = data;
      const body = {
        customer_id: customer_id,
        component: tab,
        record_ids: [id],
      };
      const response = await httpClient.put(DELETE_UCM_LAB_COMPONENT, body);
      if (response.status === 200) {
        setReloadConsentPurpose(!reloadConsentPurpose);
        toast.dismiss();
        toast.success(response?.data?.message ?? `Consent purpose deleted successfully`);
      }
    },
  });

  const { t } = useTranslation();

  const columns: ColumnDef<ConsentPurposeData>[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('ucm.ConsentPurpose.ConsentPurpose')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const rowData = row?.original;

        return (
          <div className="flex flex-row items-center gap-2">
            {rowData?.ai_generated && <GradientSparkles />}
            <p>{rowData?.name?.trim() ? rowData?.name : '-'}</p>
          </div>
        );
      },
    },
    {
      accessorKey: 'description',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('ucm.ConsentPurpose.Description')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: (info) => {
        const description: string = info.getValue<string>();

        return <div>{description?.trim() ? description : '-'}</div>;
      },
    },
    {
      accessorKey: 'processing_purpose',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('ucm.ConsentPurpose.ProcessingPurpose')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: (info) => {
        const purposes = info.getValue<AddProcessingPurposeData>();
        return <ProcessingPurposeCategoryCell purposes={purposes ? [purposes] : []} />;
      },
    },
    {
      accessorKey: 'global_expiration_period',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('ucm.ConsentPurpose.ExpiryPeriod')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: (info) => {
        const global_expiration_period: number = info.getValue<number>();
        return (
          <div className="flex flex-row items-center gap-2">
            <span className="text-sm font-medium">{global_expiration_period}</span>
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: ({ column }) => (
        <Button variant="ghost" className="p-0">
          {t('ucm.ConsentPurpose.Action')}
        </Button>
      ),

      cell: ({ row }) => {
        const category = row.original;
        return (
          <div className="flex flex-row">
            <Button type="button" onClick={() => handleEdit(category)}>
              <Pencil size={16} />
            </Button>
            <CommonConfirmationDialog
              title={t('ucm.ConsentPurpose.DeleteConsent')}
              confirmationText={t('ucm.ConsentPurpose.SureToDelete')}
              handleYesClick={() => {
                handleDeleteLabComponent({
                  tab: 'consent_purpose',
                  id: category?.id,
                });
                setReloadConsentPurpose(!reloadConsentPurpose);
              }}
              triggerText={<Trash size={18} />}
              triggerClass=""
            />
          </div>
        );
      },
    },
  ];

  const { data: consentPurposeList, isLoading: consentPurposeListLoading } = useQuery({
    queryKey: ['concentPurposesList', reloadLabList, reloadConsentPurpose, searchValue],
    queryFn: async () => {
      const response = await fetchConsentPurposeData(customer_id, searchValue);

      if (response.status !== 200) {
        throw new Error('Failed to fetch category data data.');
      }

      const consentData = response?.data?.result?.data?.map((item: any) => {
        return {
          id: item?.id,
          name: item?.name,
          global_expiration_period: item?.global_expiration_period,
          description: item?.description,
          ai_generated: item?.ai_generated,
          reviewed: item?.reviewed,
          processing_purpose: item?.processing_purpose_id
            ? {
                id: item?.processing_purpose_id,
                name: item?.processing_purpose_name,
              }
            : null,
        };
      });
      return consentData;
    },
    enabled: !!customer_id,
    initialData: [],
  });

  // useEffect(() => {
  //   const fetchConsentData = async () => {
  //     try {
  //       setIsLoading(true);
  //       const response = await fetchConsentPurposeData(customer_id, searchValue);
  // if (response.status !== 200) {
  //   throw new Error('Failed to fetch category data data.');
  // }

  //       const consentData = response?.data?.result?.data?.map((item: any) => {
  //         return {
  //           id: item?.id,
  //           name: item?.name,
  //           description: item?.description,
  //           ai_generated: item?.ai_generated,
  //           reviewed: item?.reviewed,
  //           processing_purpose: item?.processing_purpose_id
  //             ? {
  //                 id: item?.processing_purpose_id,
  //                 name: item?.processing_purpose_name,
  //               }
  //             : null,
  //         };
  //       });

  //       setConsentPurposeList(consentData);
  //     } catch (error) {
  //       if (error instanceof Error) {
  //         console.error('Fetching processing category data failed!');
  //       } else {
  //         console.error('An unknown error occurred:', error);
  //       }
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };

  //   if (customer_id !== 0) {
  //     fetchConsentData();
  //   }
  // }, [reloadLabList, reloadConsentPurpose, searchValue]);

  return (
    <>
      {isAlertDialogOpen && (
        <AlertDialog open={isAlertDialogOpen} onOpenChange={setIsAlertDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="text-lg">Alert</AlertDialogTitle>
            </AlertDialogHeader>
            {singleConsentPurposeData?.message}

            <AlertDialogFooter>
              <Button
                variant="outline"
                className="text-primary"
                onClick={() => setIsAlertDialogOpen(false)}
              >
                {t('Common.Cancel')}
              </Button>
              <Button
                variant="default"
                className="btn-background-effect bg-custom-primary text-primary-background"
                onClick={(event) => {
                  setIsAlertDialogOpen(false);
                  setIsDialogOpen(false);
                  event.stopPropagation();
                  event.preventDefault();
                  handleSubmitData(UPDATE_UCM_CONSENT_PURPOSE, {
                    customer_id,
                    ...consentPurposeData,
                    global_expiration_period:
                      Number(consentPurposeData.global_expiration_period) || 0,
                  });
                }}
              >
                {t('Common.Save')}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
      {isDialogOpen && (
        <ShadcnDialog
          title={t('ucm.ConsentPurpose.UpdateConsentPurpose')}
          open={isDialogOpen}
          onOpenChange={handleOpenChange}
          footer={
            <footer className="flex w-full flex-row justify-end gap-2">
              <Button
                variant="outline"
                className="text-primary"
                onClick={() => handleOpenChange(false)}
              >
                {t('Common.Cancel')}
              </Button>
              <Button
                variant="default"
                className="btn-background-effect bg-custom-primary text-primary-background"
                onClick={(event) => {
                  setIsSubmitted(true);
                  if (
                    !consentPurposeData?.name?.trim() ||
                    !consentPurposeData?.description?.trim()
                  ) {
                    event.stopPropagation();
                    toast.dismiss();
                    toast.error(
                      t('FrontEndErrorMessage.UniversalConsentManagement.fillAllRequiredFields')
                    );
                    return;
                  } else {
                    handleSubmitData(UPDATE_UCM_CONSENT_PURPOSE, {
                      customer_id,
                      ...consentPurposeData,
                    });
                  }
                }}
              >
                {t('Common.Save')}
              </Button>
            </footer>
          }
          dialogContentClassName="sm:max-w-[30%] font-primary-text"
        >
          <section className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              <Label
                htmlFor="name"
                className={`text-left ${
                  isSubmitted && consentPurposeData?.name?.trim() === ''
                    ? 'text-red-500'
                    : 'text-black'
                } text-sm font-medium leading-tight`}
              >
                {t('ucm.ConsentPurpose.Name')}
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                name="name"
                type="text"
                value={consentPurposeData?.name}
                placeholder={t('ucm.ConsentPurpose.EnterCategoryName')}
                className={`border border-solid ${
                  isSubmitted && consentPurposeData?.name?.trim() === ''
                    ? 'border-red-500'
                    : 'border-border'
                }`}
                onChange={(event) => handleDataChange(event, setConsentPurposeData)}
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label
                htmlFor="description"
                className={`text-left ${
                  isSubmitted && consentPurposeData?.description?.trim() === ''
                    ? 'text-red-500'
                    : 'text-black'
                } text-sm font-medium leading-tight`}
              >
                {t('ucm.ConsentPurpose.Description')}
                <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="description"
                name="description"
                value={consentPurposeData?.description}
                placeholder={t('ucm.ConsentPurpose.EnterCategoryDescription')}
                className={`border border-solid ${
                  isSubmitted && consentPurposeData?.description?.trim() === ''
                    ? 'border-red-500'
                    : 'border-border'
                }`}
                onChange={(event) => handleDataChange(event, setConsentPurposeData)}
              />
            </div>
            <div className="flex flex-col gap-2">
              <Label
                htmlFor="global_expiration_period"
                className={`text-left ${
                  isSubmitted &&
                  (consentPurposeData?.global_expiration_period === undefined ||
                    consentPurposeData?.global_expiration_period === null)
                    ? 'text-red-500'
                    : 'text-black'
                } text-sm font-medium leading-tight`}
              >
                Expiry Period
                <span className="text-red-500">*</span>
              </Label>
              <div className="flex items-center gap-2">
                <Input
                  id="global_expiration_period"
                  name="global_expiration_period"
                  type="number"
                  min={1}
                  max={12000}
                  step="1"
                  value={consentPurposeData?.global_expiration_period}
                  placeholder="Enter expiry period"
                  className={`border border-solid ${
                    isSubmitted &&
                    (consentPurposeData?.global_expiration_period === undefined ||
                      consentPurposeData?.global_expiration_period === null)
                      ? 'border-red-500'
                      : 'border-border'
                  }`}
                  onChange={(event) => handleDataChange(event, setConsentPurposeData)}
                  onKeyDown={(e) => {
                    if (e.key === '-' || e.key === '+' || e.key === 'e' || e.key === 'E') {
                      e.preventDefault();
                    }
                  }}
                  onPaste={(e) => {
                    const pastedText = e.clipboardData.getData('text');
                    if (
                      pastedText.includes('-') ||
                      pastedText.includes('+') ||
                      pastedText.includes('e') ||
                      pastedText.includes('E')
                    ) {
                      e.preventDefault();
                    }
                  }}
                />
                <span>Days</span>
              </div>
            </div>
          </section>
        </ShadcnDialog>
      )}
      <section className="size-full overflow-auto">
        <DynamicTable<ConsentPurposeData>
          data={consentPurposeList}
          loading={consentPurposeListLoading}
          columns={columns}
          enableSorting
          enablePagination
          searchTerm={searchValue}
        />
      </section>
    </>
  );
};

export default ConsentPurpose;
