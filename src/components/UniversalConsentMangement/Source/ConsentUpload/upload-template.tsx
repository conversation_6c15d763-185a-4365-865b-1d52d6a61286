import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import axios from 'axios';
import toast from 'react-hot-toast';
import { useDispatch } from 'react-redux';
import close from '../../../../assets/close.svg';
import systemIcon from '../../../../assets/System Icons.svg';
import { UploadCollectionTemplateProperties } from '../../../../types/universal-consent-management';
import { upload_collection_template } from '../../../common/services/universal-consent-management';

const UploadCollectionTemplate: React.FC<UploadCollectionTemplateProperties> = ({
  onClose,
  ct_id,
  customer_id,
}) => {
  const dispatch = useDispatch();

  const [errorMessage, setErrorMessage] = useState('');
  const [file, setFile] = useState<File | undefined>();
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  // const [url, setUrl] = useState<string>('');

  // const handlePasteURl = (event: React.ChangeEvent<HTMLInputElement>) => {
  //   setUrl(event.target.value);
  // };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileChange(event);
    }
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement> | React.DragEvent<HTMLDivElement>
  ) => {
    const file: File | undefined =
      event.type === 'change'
        ? (event as React.ChangeEvent<HTMLInputElement>).target.files?.[0]
        : (event as React.DragEvent<HTMLDivElement>).dataTransfer.files?.[0];
    if (file) {
      const acceptedFormats = ['.csv'];
      const fileExtension = file.name.slice(Math.max(0, file.name.lastIndexOf('.')));
      if (acceptedFormats.includes(fileExtension)) {
        setSelectedFile(file.name);
        setErrorMessage('');
        setFile(file);
        // setIsSubmitting(false)
      } else {
        // setFileName("");
        setErrorMessage(t('FrontEndErrorMessage.UniversalConsentManagement.invalidFileFormat'));
        // setIsSubmitting(true)
      }
    } else {
      //   setFileName("");
      setErrorMessage('');
    }
  };

  async function handleSubmitAttachment() {
    if (!file) {
      toast.dismiss();
      toast.error(t('UniversalConsentManagement.uploadValidFile'));
      return;
    }

    setIsSubmitting(true);
    toast.loading(t('CommonErrorMessages.ProcessingEllipsis'));

    try {
      console.log('Selected file:', file);

      // Create FormData and append fields
      const formData = new FormData();
      formData.append('file', file);
      formData.append('customer_id', customer_id.toString());
      formData.append('ct_id', ct_id.toString());

      // Attempt to upload the file
      const response = await upload_collection_template(formData, customer_id, ct_id);
      console.log('Upload response:', response);

      // Check response for success
      if (response.status_code === 200 && response?.result) {
        onClose(false);
        setIsSubmitting(false);
        toast.dismiss();
        toast.success(t('FrontEndErrorMessage.UniversalConsentManagement.uploadedSuccessfully'));
      } else {
        console.error('Error:', response.status, response.statusText);
        setIsSubmitting(false);
        toast.dismiss();
        toast.error(
          t('UniversalConsentManagement.uploadFailed', {
            error: response.statusText || t('UniversalConsentManagement.unknownError'),
          })
        );
      }
    } catch (error) {
      setIsSubmitting(false);
      toast.dismiss();

      if (axios.isAxiosError(error)) {
        // Handle Axios-specific error
        const errorMessage = error?.response?.data?.result?.error || error.message;
        console.error('Axios Error:', error);
        toast.error(t('FrontEndErrorMessage.FileUpload.FailedToUploadFile'));
      } else {
        // Handle general error
        console.error('Unexpected Error:', error);
        toast.error(t('UniversalConsentManagement.unexpectedErrorOccurred'));
      }
    }
  }

  const { t } = useTranslation();

  return (
    <div className="flex w-full flex-col gap-[70px] p-5">
      <div className="flex shrink-0 flex-col items-start justify-center gap-[40px]">
        <div className="flex w-full shrink-0 flex-row items-start justify-between">
          <div className="text-center font-[Poppins] text-base font-semibold leading-[35px] text-quaternary-text">
            {t('VendorRiskManagement.Assessment.upload')}
          </div>
          <div
            className="flex size-6 shrink-0 items-center justify-center rounded-md border border-solid border-[#F3F6F9] bg-[#E3E3E3]"
            onClick={() => onClose(false)}
          >
            <img src={close} alt="Close sign" />
          </div>
        </div>

        <div
          className="flex h-[228px] w-full flex-col items-center justify-center gap-2.5 rounded-md border border-dashed border-[#60A5FA] bg-white px-0 py-3"
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onDragLeave={handleDragLeave}
        >
          <img src={systemIcon} alt="Upload"></img>
          <div className="text-center font-[Poppins] text-base font-normal leading-[26px] tracking-[-0.32px] text-[#4B5563]">
            {t('Policy.AddAttachment.DropHere')}{' '}
            <label htmlFor="avatar" className={''}>
              <span className="cursor-pointer text-[#2563EB]">
                {t('Policy.AddAttachment.Upload')}
              </span>
            </label>
            <input
              type="file"
              id="avatar"
              name="avatar"
              accept=".csv"
              multiple
              className={'hidden'}
              onChange={(event) => handleFileChange(event)}
            />
            {errorMessage && <p style={{ color: 'red' }}>{errorMessage}</p>}
            {selectedFile && <p>Selected file: {selectedFile}</p>}
          </div>
          <p className="text-center font-[Poppins] text-xs font-normal leading-[20px] tracking-[-0.24px] text-[#4B5563]">
            {t('Policy.AddAttachment.FileType')}
          </p>
        </div>

        <div className="flex h-[46px] w-full flex-row justify-end gap-3">
          <button
            className="w-[35%] rounded-lg bg-custom-primary font-[poppins] text-white"
            onClick={handleSubmitAttachment}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Submitting...' : t('Common.Submit')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default UploadCollectionTemplate;
