import axios from 'axios';
import React, { useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Badge } from '../../../../@/components/ui/badge';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/ButtonSort';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../@/components/ui/Common/Elements/Select/Select';
import httpClient from '../../../../api/httpClient';
import { RootState } from '../../../../redux/store';
import { GET_BANNER_DETAILS_WITH_LANG } from '../../../common/api';
import {
  get_languages,
  get_legal_frameworks,
  save_translated_data,
  translate_cookie_category,
  translate_cookie_itself,
  translate_cookie_service,
  update_translated_data,
} from '../../../common/services/cookie-consent-management';
import Spinner from '../../../common/spinner';
import { useStepper } from '../../../common/Stepper';
import { StepperFormActions } from '../cookie-configuration-stepper-form';
import styles from '../cookie-configuration.module.css';
import { LegalFrameworksProperties } from '../domain-detail-form';

interface BannerDesign {
  consentTabHeading: string;
  detailsTabHeading: string;
  aboutTabHeading: string;
  bannerHeading: string;
  bannerDescription: string;
  denyButtonLabel: string;
  allowSelectionButtonLabel: string;
  allowAllButtonLabel: string;
  aboutSectionContent: string;
  backgroundColor: string;
  fontColor: string;
  textSize: string;
  fontFamily: string;
  cookiePolicyUrl: string;
  logoUrl: string;
  colorScheme: string;
  buttonColor: string;
  showLogo: string;
  layoutType: string;
}

interface BannerConfigurations {
  bannerDesign: BannerDesign;
}

interface CategoryConsentRecord {
  category_id: number;
  category_name: string;
  category_description: string;
  category_translation_id: number;
  services: Service[];
}

interface Service {
  service_id: number;
  service_name: string;
  service_description: string;
  service_translation_id: number;
  cookies: Cookie[];
}

interface Cookie {
  cookie_id: number;
  cookie_key: string;
  description: string;
  cookie_translation_id: number;
}

interface CookieBanner {
  domain_id: number;
  banner_title: string;
  banner_description: string;
  banner_code: string;
  banner_configuration: BannerConfigurations;
  language_code: string;
  domain_registration_step: string;
  banner_translation_id: number;
  category_consent_record: CategoryConsentRecord[];
}

interface LanguageProperties {
  language_code: string;
  language: string;
}

const LanguageSupport = () => {
  const { t } = useTranslation();

  //! Variables
  const allowedKeys = [
    'aboutSectionContent',
    'aboutTabHeading',
    'consentTabHeading',
    'allowAllButtonLabel',
    'allowSelectionButtonLabel',
    'banner_configuration',
    'bannerDesign',
    'bannerDescription',
    'bannerHeading',
    'denyButtonLabel',
    'detailsTabHeading',
    'category_consent_record',
    'category_description',
    'category_name',
    'banner_title',
    'banner_description',
    'service_name',
    'service_description',
    'description',
    'service_name',
    'service_description',
    'description',
    'services',
    'cookies',
    'cookie_key',
    'cookie_type',
    'vendor_name',
  ];

  //! Selectors
  const domain_id: number = useSelector(
    (state: RootState) =>
      state.cookieConsentManagement.CookieConsentDomain.cookieConfiguration.domain_id
  );

  const { nextStep } = useStepper();

  const textareaRefs = useRef<Record<string, HTMLTextAreaElement | null>>({});

  //! States
  const [bannerDetails, setBannerDetails] = useState<CookieBanner | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('en');
  const [loading, setLoading] = useState<boolean>(true);
  const [translationLoading, setTranslationLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [languages, setLanguages] = useState<LanguageProperties[]>([]);
  const [translatedData, setTranslatedData] = useState<CookieBanner | null>(null);
  const [supportedLanguages, setSupportedLanguages] = useState<LanguageProperties[]>([]);
  const [refetchSupportedLanguages, setRefetchSupportedLanguages] = useState<boolean>(false);
  const [selectedRegulation, setSelectedRegulation] = useState<number>(-1);
  const [legalFrameworks, setLegalFrameworks] = useState<LegalFrameworksProperties[]>([]);

  // Helper function to chunk array into groups of specified size
  const chunkArray = <T,>(array: T[], chunkSize: number): T[][] => {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  };
  const customer_id = useSelector(
    (state: RootState) => state.auth.login.login_details?.customer_id
  );
  const domain_data = useSelector(
    (state: RootState) =>
      state?.cookieConsentManagement?.CookieConsentDomain?.cookieConfiguration?.stepsData[0]
  );
  const regulationIds = domain_data?.legal_framework_id || [];

  // Set the selected regulation to the first regulation ID when regulationIds changes
  useEffect(() => {
    if (regulationIds.length > 0) {
      // Only set the regulation if we have valid regulation IDs
      setSelectedRegulation(regulationIds[0]);
    }
  }, [regulationIds]);

  const adjustTextareaHeight = (key: string) => {
    const el = textareaRefs.current[key];
    if (el) {
      el.style.height = 'auto';
      el.style.height = `${el.scrollHeight}px`;
    }
  };

  const translateCookieData = async (translateData: CookieBanner | null = bannerDetails) => {
    // Step 1: Create the request body using only allowed keys.
    const { requestBody, mappingPaths } = createTranslateRequestBody(
      translateData,
      allowedKeys,
      selectedLanguage
    );

    try {
      setTranslationLoading(true);

      // Get the texts to translate
      const textsToTranslate = requestBody.q;

      // Chunk the texts into groups of 100
      const textChunks = chunkArray(textsToTranslate, 100);

      // Array to hold all translated texts
      let allTranslatedTexts: string[] = [];

      // Process each chunk
      for (let i = 0; i < textChunks.length; i++) {
        const chunk = textChunks[i];

        // Create a request body for this chunk
        const chunkRequestBody = {
          q: chunk,
          target: selectedLanguage,
        };

        // Call the Google Translate API for this chunk
        const response = await fetch(
          `https://translation.googleapis.com/language/translate/v2?key=${import.meta.env.VITE_GOOGLE_TRANSLATE_API_KEY}`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(chunkRequestBody),
          }
        );
        const data = await response.json();

        // Extract the translated texts from this chunk
        const chunkTranslatedTexts: string[] = data.data.translations.map(
          (item: { translatedText: string }) => item.translatedText
        );

        // Add to our collection of all translated texts
        allTranslatedTexts = [...allTranslatedTexts, ...chunkTranslatedTexts];
      }

      // Step 4: Update the original cookieData with all the translated texts.
      const updatedCookieData = updateCookieDataWithTranslations(
        translateData,
        mappingPaths,
        allTranslatedTexts
      );

      // Update state with the translated data.
      setTranslatedData(updatedCookieData);
    } catch (error) {
      console.error('Translation error:', error);
    } finally {
      setTranslationLoading(false);
    }
  };

  function flattenForTranslation(
    data: any,
    allowedKeys: string[],
    parentKey = ''
  ): { texts: string[]; paths: string[] } {
    let texts: string[] = [];
    let paths: string[] = [];

    if (Array.isArray(data)) {
      data.forEach((item, index) => {
        const { texts: t, paths: p } = flattenForTranslation(
          item,
          allowedKeys,
          `${parentKey}[${index}]`
        );
        texts = texts.concat(t);
        paths = paths.concat(p);
      });
    } else if (typeof data === 'object' && data !== null) {
      for (const key in data) {
        if (data.hasOwnProperty(key)) {
          const fullPath = parentKey ? `${parentKey}.${key}` : key;
          // Only add string values if the key is allowed.
          if (typeof data[key] === 'string' && allowedKeys.includes(key)) {
            texts.push(data[key]);
            paths.push(fullPath);
          } else if (typeof data[key] === 'object' && data[key] !== null) {
            const { texts: t, paths: p } = flattenForTranslation(data[key], allowedKeys, fullPath);
            texts = texts.concat(t);
            paths = paths.concat(p);
          }
        }
      }
    }
    return { texts, paths };
  }

  function createTranslateRequestBody(
    cookieData: CookieBanner | null,
    allowedKeys: string[],
    targetLanguage = 'fr'
  ) {
    const { texts, paths } = flattenForTranslation(cookieData, allowedKeys);
    const requestBody = {
      q: texts,
      target: targetLanguage,
    };
    return { requestBody, mappingPaths: paths };
  }

  function setNestedValue(obj: any, path: string, value: string): void {
    const pathParts = path.replace(/\[(\w+)\]/g, '.$1').split('.');
    let current = obj;
    for (let i = 0; i < pathParts.length - 1; i++) {
      if (!(pathParts[i] in current)) {
        current[pathParts[i]] = {};
      }
      current = current[pathParts[i]];
    }
    current[pathParts[pathParts.length - 1]] = value;
  }

  function updateCookieDataWithTranslations(
    cookieData: CookieBanner | null,
    mappingPaths: string[],
    translatedTexts: string[]
  ) {
    const updatedData = JSON.parse(JSON.stringify(cookieData)); // deep clone
    mappingPaths.forEach((path, index) => {
      setNestedValue(updatedData, path, translatedTexts[index]);
    });
    return updatedData;
  }

  // Recursively traverses the object and returns a JSX element tree.
  const updateNestedValue = (obj: any, keyPath: string[], newValue: any): any => {
    if (keyPath.length === 0) return newValue;

    const [firstKey, ...restKeys] = keyPath;

    return {
      ...obj,
      [firstKey]: updateNestedValue(obj?.[firstKey] ?? {}, restKeys, newValue),
    };
  };

  const traverseAndCreateDiv = (
    obj: any,
    allowedKeys: string[],
    isTranslated: boolean = false,
    translatedData?: CookieBanner | null,
    setTranslatedData?: React.Dispatch<React.SetStateAction<CookieBanner | null>>,
    path: string[] = [] // ✅ Track path
  ): React.ReactElement => {
    if (obj === null || typeof obj !== 'object') return <></>;

    if (Array.isArray(obj)) {
      return (
        <>
          {obj.map((item, index) => (
            <React.Fragment key={index}>
              {traverseAndCreateDiv(
                item,
                allowedKeys,
                isTranslated,
                translatedData,
                setTranslatedData,
                [...path, String(index)] // ✅ Track array index path
              )}
            </React.Fragment>
          ))}
        </>
      );
    }

    return (
      <div className="flex h-fit w-full flex-col gap-4">
        {Object.keys(obj).map((key) => {
          const value = obj[key as keyof CookieBanner];

          // 🧠 Get translated value safely
          let translatedValue: string = '';
          let nestedTranslated = translatedData?.[key as keyof CookieBanner];
          if (translatedData && key in translatedData) {
            const raw = translatedData[key as keyof CookieBanner];
            if (typeof raw === 'string') {
              translatedValue = raw;
            } else if (typeof raw === 'number') {
              translatedValue = raw.toString();
            } else {
              translatedValue = JSON.stringify(raw);
            }
          }

          const textareaValue = translatedValue || String(value);

          // ✅ Handle nested objects
          let translatedSubObject: CookieBanner | null = null;
          if (
            nestedTranslated &&
            typeof nestedTranslated === 'object' &&
            !Array.isArray(nestedTranslated)
          ) {
            translatedSubObject = nestedTranslated as unknown as CookieBanner;
          }

          const uniqueKey = [...path, key].join('.'); // Unique key for ref map

          return allowedKeys.includes(key) && typeof value === 'string' && value.trim() !== '' ? (
            <div key={uniqueKey} className="grid w-full grid-cols-2 gap-4">
              {/* Original (Left) */}
              <div className="flex flex-col gap-1">
                <p className="font-semibold">{formatKey(key)}</p>
                <p className="rounded-lg border border-primary-border bg-primary-background p-2">
                  {value}
                </p>
              </div>

              {/* Translated (Right) */}
              <div className="flex flex-col gap-1">
                <p className="font-semibold">{formatKey(key)}</p>
                {isTranslated && translatedData && setTranslatedData ? (
                  <textarea
                    ref={(el) => {
                      textareaRefs.current[uniqueKey] = el;
                      if (el) {
                        setTimeout(() => {
                          el.style.height = 'auto';
                          el.style.height = `${el.scrollHeight}px`;
                        }, 0);
                      }
                    }}
                    className="h-fit w-full resize-none overflow-hidden rounded-lg border border-primary-border bg-primary-background p-2"
                    value={textareaValue}
                    onChange={(e) => {
                      textareaRefs.current[uniqueKey] = e.target;
                      adjustTextareaHeight(uniqueKey);

                      setTranslatedData((prev) =>
                        updateNestedValue(prev || {}, [...path, key], e.target.value)
                      );
                    }}
                  />
                ) : (
                  <p className="rounded-lg border border-primary-border bg-primary-background p-2">
                    {translatedValue || ''}
                  </p>
                )}
              </div>
            </div>
          ) : typeof value === 'object' && value !== null && Object.keys(value).length > 0 ? (
            <div key={uniqueKey} className="col-span-2">
              {traverseAndCreateDiv(
                value,
                allowedKeys,
                isTranslated,
                translatedSubObject,
                setTranslatedData,
                [...path, key]
              )}
            </div>
          ) : null;
        })}
      </div>
    );
  };

  // Formats the key for a more user-friendly output.
  function formatKey(key: string): string {
    let formatted: string;
    if (key.includes('_')) {
      // Handle snake_case by replacing underscores with spaces.
      formatted = key.split('_').join(' ');
    } else {
      // Handle camelCase by inserting a space before each uppercase letter.
      formatted = key.replace(/([a-z])([A-Z])/g, '$1 $2');
    }
    formatted = formatted.trim().toLowerCase();
    return formatted.charAt(0).toUpperCase() + formatted.slice(1);
  }

  //! Handlers

  const handleSaveTranslation = async () => {
    toast.dismiss();
    toast.loading(t('Cookies.SavingTranslation'));

    const {
      category_consent_record,
      banner_configuration,
      banner_translation_id,
      ...restTranslatedData
    } = translatedData || {};
    const requestBody = {
      ...restTranslatedData,
      banner_configurations: banner_configuration,
      language_code: selectedLanguage,
      translation_id: banner_translation_id,
      domain_id,
      regulation_id: selectedRegulation,
      domain_registration_step: '5',
    };

    // Dynamically create payloads for cookie, service, and category translations from category_consent_record

    // Category translations
    const translatedCategories = {
      language_code: selectedLanguage,
      regulation_id: selectedRegulation,
      translations:
        category_consent_record
          ?.map((category) => ({
            cookie_category_id: category.category_id,
            cookie_category_translation_id: category.category_translation_id,
            name_translated: category.category_name,
            description_translated: category.category_description,
          }))
          .filter(
            (item) =>
              item.cookie_category_id != null &&
              item.name_translated != null &&
              item.description_translated != null
          ) || [],
    };

    // Service translations
    const translatedServices = {
      language_code: selectedLanguage,
      regulation_id: selectedRegulation,
      translations:
        category_consent_record?.flatMap(
          (category) =>
            category.services
              ?.map((service) => ({
                cookie_service_id: service.service_id,
                cookie_service_translation_id: service.service_translation_id,
                name_translated: service.service_name,
                description_translated: service.service_description,
              }))
              .filter(
                (item) =>
                  item.cookie_service_id != null &&
                  item.name_translated != null &&
                  item.description_translated != null
              ) || []
        ) || [],
    };

    // Cookie translations
    const translatedCookies = {
      language_code: selectedLanguage,
      regulation_id: selectedRegulation,
      translations:
        category_consent_record?.flatMap(
          (category) =>
            category.services?.flatMap(
              (service) =>
                service.cookies
                  ?.map((cookie) => ({
                    domain_cookie_id: cookie.cookie_id,
                    cookie_translation_id: cookie.cookie_translation_id,
                    name_translated: cookie.cookie_key,
                    description_translated: cookie.description,
                  }))
                  .filter(
                    (item) =>
                      item.domain_cookie_id != null &&
                      item.name_translated != null &&
                      item.description_translated != null
                  ) || []
            ) || []
        ) || [],
    };

    try {
      // Save or update the main banner translation data
      if (supportedLanguages.some((item) => item.language_code === selectedLanguage)) {
        await update_translated_data(requestBody);

        // Only call APIs if translation arrays have length
        const promisesToExecute = [];

        if (translatedCategories.translations.length > 0) {
          promisesToExecute.push(translate_cookie_category(translatedCategories, 'put'));
        }

        if (translatedServices.translations.length > 0) {
          promisesToExecute.push(translate_cookie_service(translatedServices, 'put'));
        }

        if (translatedCookies.translations.length > 0) {
          promisesToExecute.push(translate_cookie_itself(translatedCookies, 'put'));
        }

        if (promisesToExecute.length > 0) {
          await Promise.all(promisesToExecute);
        }
      } else {
        await save_translated_data(requestBody);

        // Only call APIs if translation arrays have length
        const promisesToExecute = [];

        if (translatedCategories.translations.length > 0) {
          promisesToExecute.push(translate_cookie_category(translatedCategories, 'post'));
        }

        if (translatedServices.translations.length > 0) {
          promisesToExecute.push(translate_cookie_service(translatedServices, 'post'));
        }

        if (translatedCookies.translations.length > 0) {
          promisesToExecute.push(translate_cookie_itself(translatedCookies, 'post'));
        }

        if (promisesToExecute.length > 0) {
          await Promise.all(promisesToExecute);
        }
      }

      setRefetchSupportedLanguages((prev) => !prev);
      toast.dismiss();
      toast.success(t('ToastMessages.Cookies.TranslationSavedSuccessfully'));
    } catch (error) {
      // eslint-disable-next-line unicorn/prefer-ternary
      if (axios.isAxiosError(error)) {
        // Axios specific error handling
        toast.dismiss(); // Clear any existing toasts
        // const status = error?.response?.data?.status_code;
        // const statusText = error?.response?.data?.message;
        const errorMessage = error?.response?.data?.result?.error || error.message;
        toast.error(`${errorMessage}`);
        console.error('Axios Error:', error);
      } else {
        // Generic error handling
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.ApiErrors.AnErrorOccurred'));
        console.error('Unexpected Error:', error);
      }
    }
  };

  const handleNext = () => {
    nextStep();
  };

  useEffect(() => {
    const handleTranslation = async () => {
      try {
        if (
          supportedLanguages?.some((item) => item.language_code === selectedLanguage) &&
          selectedRegulation !== -1
        ) {
          setLoading(true);
          const translatedData = await httpClient.get(
            `${GET_BANNER_DETAILS_WITH_LANG}?domain_id=${domain_id}&language_code=${selectedLanguage}&regulation_id=${selectedRegulation}`
          );

          setTranslatedData(translatedData?.data?.result?.data);
        } else {
          await translateCookieData(bannerDetails); // Translate the cookie data
        }
      } catch (error) {
        setError(t('Cookies.UnableToTranslate'));
        console.error(error);
      } finally {
        setLoading(false);
        setTranslationLoading(false);
      }
    };
    if (bannerDetails && selectedLanguage) handleTranslation();
  }, [selectedLanguage, bannerDetails, selectedRegulation]);

  //! Effects
  useEffect(() => {
    const fetchCookieData = async () => {
      setLoading(true);
      try {
        const response = await httpClient.get(
          `${GET_BANNER_DETAILS_WITH_LANG}?domain_id=${domain_id}`
        );
        setBannerDetails(response?.data?.result?.data);
        setLoading(false);
      } catch (err) {
        console.error(err);
        setError(t('Cookies.UnableToFetchData'));
        setLoading(false);
      }
    };

    fetchCookieData();
  }, [domain_id]);
  useEffect(() => {
    const fetchLegalFrameworks = async () => {
      try {
        const responseData = await get_legal_frameworks(customer_id);
        setLegalFrameworks(responseData?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchLegalFrameworks();
  }, [customer_id]);

  useEffect(() => {
    const fetchLanguages = async () => {
      try {
        const response = await get_languages();
        setLanguages(response?.result?.data);
      } catch (error) {
        console.log(error);
      }
    };

    fetchLanguages();
  }, []);

  useEffect(() => {
    const fetchSupportedLanguages = async () => {
      try {
        if (selectedRegulation !== -1) {
          const response = await get_languages(domain_id, selectedRegulation);
          setSupportedLanguages(response?.result?.data);
        }
      } catch (error) {
        console.log(error);
      }
    };

    fetchSupportedLanguages();
  }, [domain_id, refetchSupportedLanguages, selectedRegulation]);

  return (
    <>
      <StepperFormActions handleClick={handleNext} />
      {(() => {
        if (loading) {
          return (
            <div className="flex size-full items-center justify-center">
              <Spinner />
            </div>
          );
        } else if (error) {
          return <div>{error}</div>;
        } else if (bannerDetails) {
          return (
            <main>
              <div
                className="mt-2.5 w-full rounded-lg border border-primary-border bg-primary-background"
                style={{ height: 'calc(100vh - 23rem)' }}
              >
                <div className="flex h-fit w-full items-start justify-between gap-4 p-4">
                  <div className="flex min-w-0 flex-1 flex-col gap-4">
                    <div className="grid grid-cols-[auto_1fr] items-center gap-3">
                      <label className="whitespace-nowrap text-sm font-medium">
                        Select regulation
                      </label>
                      <Select
                        value={selectedRegulation?.toString()}
                        onValueChange={(value) => {
                          console.log('Selected regulation ID:', value);
                          setSelectedRegulation(Number(value));
                        }}
                      >
                        <SelectTrigger className="h-10 text-sm">
                          <SelectValue placeholder="Choose Regulation" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="-1" disabled>
                            {t('Cookies.ChooseRegulation')}
                          </SelectItem>
                          {regulationIds?.map((id: number, index: number) => (
                            <SelectItem key={index} value={id.toString()}>
                              {
                                legalFrameworks.find(
                                  (item: LegalFrameworksProperties) => item.id === id
                                )?.framework_name
                              }
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    {supportedLanguages?.length > 0 && (
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-medium">{t('Cookies.TranslatedLanguages')}:</p>
                        <div className="flex flex-wrap gap-2">
                          {supportedLanguages?.map((language) => (
                            <Badge
                              onClick={() => setSelectedLanguage(language.language_code)}
                              key={language?.language_code}
                              className={`cursor-pointer ${
                                selectedLanguage === language.language_code
                                  ? 'bg-primary text-white'
                                  : 'bg-gray-300 text-black'
                              }`}
                            >
                              <span className="truncate">{language?.language}</span>
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex shrink-0 items-center gap-3">
                    <select
                      className="h-10 min-w-32 rounded-lg border border-primary-border bg-primary-background px-3 text-sm"
                      onChange={(e) => setSelectedLanguage(e.target.value)}
                      value={selectedLanguage}
                      disabled={translationLoading}
                    >
                      {languages.map((language) => (
                        <option key={language.language_code} value={language.language_code}>
                          {language.language}
                        </option>
                      ))}
                    </select>
                    <Button
                      onClick={handleSaveTranslation}
                      variant="default"
                      className="h-10 text-white"
                      disabled={translationLoading}
                    >
                      {t('Cookies.SaveTranslation')}
                    </Button>
                  </div>
                </div>
                <div
                  className={`p-3 ${styles.table_main_content} overflow-auto`}
                  style={{ height: 'calc(100vh - 33rem)' }}
                >
                  {translationLoading ? (
                    <div className="flex size-full items-center justify-center">
                      <Spinner />
                    </div>
                  ) : (
                    traverseAndCreateDiv(
                      (() => {
                        const { category_consent_record, ...restBannerDetails } =
                          bannerDetails || {};
                        return restBannerDetails;
                      })(),
                      allowedKeys,
                      true,
                      translatedData,
                      setTranslatedData
                    )
                  )}
                </div>
              </div>
            </main>
          );
        } else {
          return <div>{t('Cookies.NoData')}</div>;
        }
      })()}
    </>
  );
};

export default LanguageSupport;
