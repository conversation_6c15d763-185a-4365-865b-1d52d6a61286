import React, { useState } from 'react';
import { useTipTapEditor } from './TipTapEditor';

const HeadingTest: React.FC = () => {
  const [content, setContent] = useState(`
    <p>Main Title Text</p>
    <p>Subtitle Text</p>
    <p>This is normal paragraph text that should remain unchanged.</p>
    <p>Another line to test with.</p>
  `);

  const { editor, menuBar, editorContent } = useTipTapEditor({
    initialContent: content,
    placeholder: 'Type some text and test the heading buttons...',
    onContentChange: (newContent) => {
      setContent(newContent);
    },
    onEditorReady: (editorInstance) => {
      console.log('Editor ready with commands:', Object.keys(editorInstance.commands));
      console.log('Can toggle H1:', editorInstance.can().toggleHeading({ level: 1 }));
      console.log('Can toggle H2:', editorInstance.can().toggleHeading({ level: 2 }));
      console.log('Can toggle ordered list:', editorInstance.can().toggleOrderedList());
    },
    enabledMenuItems: [
      'bold',
      'italic',
      'heading1',
      'heading2',
      'orderedList',
      'bulletList',
      'undo',
      'redo',
    ],
  });

  return (
    <div className="p-4 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Heading & List Test</h1>
      
      <div className="border border-gray-300 rounded-lg p-4 mb-4">
        <h3 className="text-lg font-semibold mb-2">Editor with H1, H2, and Ordered List</h3>
        {menuBar}
        <div className="border border-gray-200 rounded p-4 min-h-[200px] mt-2">
          {editorContent}
        </div>
      </div>

      <div className="mt-4">
        <h3 className="text-lg font-semibold mb-2">Instructions</h3>
        <ul className="list-disc pl-6 space-y-1">
          <li><strong>Type multiple lines of text</strong> in the editor (e.g., "Title", "Subtitle", "Normal text")</li>
          <li><strong>Select ONLY the text you want to format</strong> (e.g., select just "Title")</li>
          <li><strong>Click H1 button</strong> - Only the selected text should become large and bold</li>
          <li><strong>Select different text</strong> (e.g., "Subtitle") and <strong>click H2 button</strong></li>
          <li><strong>Leave some text as normal</strong> to see the difference</li>
          <li><strong>Test ordered lists</strong> by clicking the numbered list button</li>
        </ul>
        <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded">
          <p className="text-sm"><strong>Expected behavior:</strong> Only the selected text should change size/style, not the entire editor content.</p>
        </div>
      </div>

      <div className="mt-4">
        <h3 className="text-lg font-semibold mb-2">Current HTML Output</h3>
        <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
          {content}
        </pre>
      </div>

      {editor && (
        <div className="mt-4">
          <h3 className="text-lg font-semibold mb-2">Editor State</h3>
          <div className="bg-gray-100 p-4 rounded text-sm">
            <p><strong>H1 Active:</strong> {editor.isActive('heading', { level: 1 }) ? 'Yes' : 'No'}</p>
            <p><strong>H2 Active:</strong> {editor.isActive('heading', { level: 2 }) ? 'Yes' : 'No'}</p>
            <p><strong>Ordered List Active:</strong> {editor.isActive('orderedList') ? 'Yes' : 'No'}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default HeadingTest;
