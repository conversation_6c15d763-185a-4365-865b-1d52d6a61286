import React, { useState } from 'react';
import { useTipTapEditor } from './TipTapEditor';

const HeadingTest: React.FC = () => {
  const [content, setContent] = useState('<p>Select text and click H1 or H2 buttons to test heading functionality!</p>');

  const { editor, menuBar, editorContent } = useTipTapEditor({
    initialContent: content,
    placeholder: 'Type some text and test the heading buttons...',
    onContentChange: (newContent) => {
      setContent(newContent);
    },
    onEditorReady: (editorInstance) => {
      console.log('Editor ready with commands:', Object.keys(editorInstance.commands));
      console.log('Can toggle H1:', editorInstance.can().toggleHeading({ level: 1 }));
      console.log('Can toggle H2:', editorInstance.can().toggleHeading({ level: 2 }));
      console.log('Can toggle ordered list:', editorInstance.can().toggleOrderedList());
    },
    enabledMenuItems: [
      'bold',
      'italic',
      'heading1',
      'heading2',
      'orderedList',
      'bulletList',
      'undo',
      'redo',
    ],
  });

  return (
    <div className="p-4 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Heading & List Test</h1>
      
      <div className="border border-gray-300 rounded-lg p-4 mb-4">
        <h3 className="text-lg font-semibold mb-2">Editor with H1, H2, and Ordered List</h3>
        {menuBar}
        <div className="border border-gray-200 rounded p-4 min-h-[200px] mt-2">
          {editorContent}
        </div>
      </div>

      <div className="mt-4">
        <h3 className="text-lg font-semibold mb-2">Instructions</h3>
        <ul className="list-disc pl-6 space-y-1">
          <li>Type some text in the editor</li>
          <li>Select the text you want to format</li>
          <li>Click the H1 button (should make text large and bold)</li>
          <li>Click the H2 button (should make text medium and semi-bold)</li>
          <li>Click the ordered list button to create numbered lists</li>
        </ul>
      </div>

      <div className="mt-4">
        <h3 className="text-lg font-semibold mb-2">Current HTML Output</h3>
        <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
          {content}
        </pre>
      </div>

      {editor && (
        <div className="mt-4">
          <h3 className="text-lg font-semibold mb-2">Editor State</h3>
          <div className="bg-gray-100 p-4 rounded text-sm">
            <p><strong>H1 Active:</strong> {editor.isActive('heading', { level: 1 }) ? 'Yes' : 'No'}</p>
            <p><strong>H2 Active:</strong> {editor.isActive('heading', { level: 2 }) ? 'Yes' : 'No'}</p>
            <p><strong>Ordered List Active:</strong> {editor.isActive('orderedList') ? 'Yes' : 'No'}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default HeadingTest;
