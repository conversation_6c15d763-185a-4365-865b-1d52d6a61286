import BulletList from '@tiptap/extension-bullet-list';
import { Color } from '@tiptap/extension-color';
import Highlight from '@tiptap/extension-highlight';
import Link from '@tiptap/extension-link';
import ListItem from '@tiptap/extension-list-item';
import OrderedList from '@tiptap/extension-ordered-list';
import Placeholder from '@tiptap/extension-placeholder';
import TextAlign from '@tiptap/extension-text-align';
import TextStyle from '@tiptap/extension-text-style';
import Underline from '@tiptap/extension-underline';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { useCallback, useMemo } from 'react';
import CookieConfigurationEditorMenubar from '../../CookieConsentManagement/CookieConsentDomain/CustomizeBanner/cookie-banner-config-editor-menubar';
import Heading from '@tiptap/extension-heading';

interface TipTapEditorProps {
  initialContent?: string;
  placeholder?: string;
  onEditorReady?: (editor: any) => void;
  onContentChange?: (content: string) => void;
  enabledMenuItems?: string[];
}

const useTipTapEditor = ({
  initialContent = '',
  placeholder = 'Write something …',
  onEditorReady,
  onContentChange,
  enabledMenuItems = [],
}: TipTapEditorProps) => {
  const extensions = useMemo(
    () => [
      StarterKit.configure({
        bulletList: false,
        orderedList: false,
      }),
      BulletList.configure({
        HTMLAttributes: {
          class: 'list-disc pl-6 mb-2',
        },
      }),
      OrderedList.configure({
        HTMLAttributes: {
          class: 'list-decimal pl-6 mb-2',
        },
      }),
      Heading.configure({
        levels: [1],
        HTMLAttributes: {
          class: 'h1',
        },
      }),
      Heading.configure({
        levels: [1],
        HTMLAttributes: {
          class: 'h1',
        },
      }),
      ListItem,
      Underline,
      TextAlign.configure({
        types: ['paragraph', 'heading'],
      }),
      Placeholder.configure({
        placeholder,
      }),
      Highlight.configure({
        multicolor: true,
      }),
      // Enhanced extensions - Link and Color
      TextStyle,
      Color,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline cursor-pointer',
          target: '_blank',
          rel: 'noopener noreferrer',
        },
      }),
    ],
    [placeholder]
  );

  const editor = useEditor({
    extensions,
    content: initialContent,
    autofocus: true,
    onUpdate: ({ editor }) => {
      const htmlContent = editor.getHTML();
      onContentChange?.(htmlContent);
    },
    onCreate: ({ editor }) => {
      onEditorReady?.(editor);
    },
    editorProps: {
      attributes: {
        class: 'focus:outline-none',
        spellcheck: 'false',
      },
    },
  });

  const handleContentChange = useCallback(
    (content: string) => {
      onContentChange?.(content);
    },
    [onContentChange]
  );

  const menuBar = editor ? (
    <CookieConfigurationEditorMenubar
      editor={editor}
      onContentChange={handleContentChange}
      enabledItems={enabledMenuItems}
    />
  ) : null;

  const editorContent = editor ? <EditorContent editor={editor} /> : null;

  return {
    editor,
    menuBar,
    editorContent,
  };
};

export { useTipTapEditor };
export default useTipTapEditor;
