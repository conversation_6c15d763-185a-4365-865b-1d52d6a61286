import axios from 'axios';
import { Globe } from 'lucide-react';
import { useEffect, useState } from 'react';

import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import ReactModal from 'react-modal';
import { Badge } from '../../@/components/ui/badge';
import { Checkbox } from '../../@/components/ui/checkbox';
import { Button } from '../../@/components/ui/Common/Elements/Button/ButtonSort';
import { Label } from '../../@/components/ui/Common/Elements/Label/Label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '../../@/components/ui/Common/Elements/Select/Select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../../@/components/ui/dropdown-menu';
import { get_supported_languages } from '../../components/common/services/universal-consent-management';
import Spinner from '../../components/common/spinner';
import {
  ConsentFormDataType,
  ConsentFormFrequencyProperties,
  CTCPMapBucket,
  PiiValuesType,
  ResponseType,
  StaticDataType,
  SubjectIdentityProperties,
} from '../../types/universal-consent-management';

interface ConsentCheckboxProps {
  contentId: string;
  environment: string;
  data?: any;
}

interface ConsentDataProperties {
  contentId: string;
  environment: string;
  additionalInfo: PiiValuesType;
}

// Extend the Window interface to include triggerConsentModal
declare global {
  interface Window {
    triggerConsentModal: (data: any) => void;
  }
}

const ConsentCheckbox: React.FC<ConsentCheckboxProps> = ({ contentId, environment, data }) => {
  const { t } = useTranslation();
  const badgeClass = 'text-[white] bg-primary';
  const [formData, setFormData] = useState<ConsentFormDataType | null>(null);
  const [frequencyList, setFrequencyList] = useState<ConsentFormFrequencyProperties[]>([]);
  const [piiValues, setPiiValues] = useState<PiiValuesType>({});
  const [consentStatus, setConsentStatus] = useState<Map<number, CTCPMapBucket[]>>(new Map());
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasError, setHasError] = useState<boolean>(false);
  const [subjectIdentityData, setSubjectIdentityData] = useState<SubjectIdentityProperties>();
  const [selectedLanguage, setSelectedLanguage] = useState<string>('en');
  const [languages, setLanguages] = useState([]);
  const [customerId, setCustomerId] = useState<number>();
  const [formId, setFormId] = useState<number>();
  const [fontSize, setFontSize] = useState<string>('14px');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [consentData, setConsentData] = useState<ConsentDataProperties>();
  const [staticData, setStaticData] = useState<StaticDataType>({
    heading1: 'Additional Personal Details',
    heading2: 'AGREEMENTS',
    frequency: 'Frequency',
    agreement_text: 'I agree to share my',
    for: 'for',
    footer_content:
      'By submitting this form, I agree to all terms and conditions. Additionally, I consent to the collection, use, and sharing of my personal and sensitive information by AEBC in accordance with GoTrust’s Data Privacy Policy, which upholds the highest standards of privacy and governance, as outlined on the website.',
    buttonText: 'Submit',
    formHeading: 'Consent Form',
  });

  console.log('Consent Checkbox Props:', contentId, environment, data);
  console.log('pii values', piiValues);

  const baseURL =
    environment === 'dev'
      ? 'https://dev.gotrust.tech/universal'
      : environment === 'preprod'
        ? 'https://preprod.gotrust.tech/universal'
        : environment === 'sandbox'
          ? 'https://sandbox.gotrust.tech/universal'
          : '';

  useEffect(() => {
    setPiiValues(consentData?.additionalInfo ?? {});
  }, [consentData]);
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setHasError(false);
      try {
        const response = await axios.get<ResponseType>(
          `${baseURL}/api/v3/gtf/display?source_id=${contentId}${selectedLanguage ? `&language_code=${selectedLanguage}` : ''}`
        );
        const data = response.data;

        const initialPiiValues: PiiValuesType = {};

        data.result.data.pii_list.forEach((pii) => {
          initialPiiValues[pii.pii_label_id] = ''; // Default empty values for PII fields
        });

        data.result.data.purpose_list.forEach((purpose) => {
          purpose.consent_bucket.forEach((consent) => {
            setConsentStatus((prev) => {
              const updatedMap = new Map(prev);

              // Modify ct_cp_map_bucket to ensure default_opt_out_allowed items have consent_status as true
              const updatedBucket = consent.ct_cp_map_bucket.map((item) => ({
                ...item,
                consent_status: item.default_opt_out_allowed ? true : item.consent_status,
              }));

              updatedMap.set(consent.consent_purpose_id, updatedBucket);

              return updatedMap;
            });
          });
        });

        setSubjectIdentityData({
          id: data.result.data.additional_info.subject_identity_type_id,
          name: data.result.data.additional_info.subject_identity_type_name,
        });
        // setPiiValues(initialPiiValues);

        setFormData(data.result.data);
        setCustomerId(data.result.data.additional_info.customer_id);
        setFormId(data.result.data.additional_info.form_id);
        setFontSize(
          data.result.data.form_configuration?.textSize === 'small'
            ? '12px'
            : data.result.data.form_configuration?.textSize === 'medium'
              ? '16px'
              : '20px'
        );
      } catch (error) {
        console.error(error);
        setHasError(true);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [contentId, selectedLanguage]);

  // fetching Supported Languages
  useEffect(() => {
    const fetchSupportedLanguages = async () => {
      try {
        // console.log('API hit');
        // Call the returned function to fetch data
        const responseData = await get_supported_languages(
          'form',
          customerId,
          undefined,
          undefined,
          formId
        );
        setLanguages(responseData?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchSupportedLanguages();
  }, [customerId, formId]);

  // fetching frequency
  useEffect(() => {
    const fetchFrequency = async () => {
      try {
        // Call the returned function to fetch data
        const response = await axios.get(
          `${baseURL}/api/v2/ct/frequency-list${selectedLanguage ? `?language_code=${selectedLanguage}` : ''}`
        );
        setFrequencyList(response?.data?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchFrequency();
  }, [contentId, selectedLanguage]);

  // // Initialize `piiValues` dynamically when formData changes
  // useEffect(() => {
  //   if (formData?.pii_list) {
  //     const initialPiiValues = formData.pii_list.reduce((acc, pii) => {
  //       acc[pii.pii_label_id] = ''; // Default empty values for all PII fields
  //       return acc;
  //     }, {} as PiiValuesType);
  //     setPiiValues(initialPiiValues);
  //   }
  // }, [formData]);

  // Expose the global function to trigger the modal
  useEffect(() => {
    window.triggerConsentModal = (data) => {
      setConsentData(data); // data passed as arguments from the Honda website
      console.log('Consent data:', data);
      setIsModalOpen(true);
    };

    // Cleanup: remove the global function when component unmounts
    return () => {
      if (window.triggerConsentModal) {
        window.triggerConsentModal = () => {};
      }
    };
  }, []);

  console.log('Consent Modal Manager loaded');

  const handleOpenChange = (open: boolean) => {
    setIsModalOpen(open);
  };

  const handleConsentChange = (consentId: number, checked: boolean) => {
    setConsentStatus((prev) => {
      // Clone the existing Map (React state requires immutability)
      const updatedMap = new Map(prev);
      let updatedValue: CTCPMapBucket[] | undefined = updatedMap?.get(consentId);

      if (updatedValue) {
        updatedValue = updatedValue.map((item) => ({
          ...item,
          consent_status: checked,
        }));

        updatedMap.set(consentId, updatedValue);
      }
      // Update existing entry OR add a new one

      return updatedMap; // Update state with new Map
    });
  };

  const handleFrequencyChange = (consentId: number, newFrequency: string) => {
    // Also update the frequency in consentStatus
    setConsentStatus((prev) => {
      // Clone the existing Map (React state requires immutability)
      const updatedMap = new Map(prev);
      let updatedValue: CTCPMapBucket[] | undefined = updatedMap?.get(consentId);

      if (updatedValue) {
        updatedValue = updatedValue.map((item) => ({
          ...item,
          frequency: newFrequency?.toLowerCase(),
        }));

        updatedMap.set(consentId, updatedValue);
      }
      // Update existing entry OR add a new one

      return updatedMap; // Update state with new Map
    });
  };
  const submitForm = async () => {
    toast.dismiss();
    if (!formData) return;

    if (!consentData) {
      console.error('No user data provided to the modal');
      return;
    }

    // Check if all required consent checkboxes are checked
    const allRequiredChecked = Array.from(consentStatus.values()).every((consentList) =>
      consentList.every((consent) =>
        consent.compulsory_consent ? consent.consent_status === true : true
      )
    );

    if (!allRequiredChecked) {
      toast.dismiss();
      toast.error(t('CommonErrorMessages.PleaseAgreeToAllRequiredConsents'));
      return;
    }

    // if (!otpVerified) {
    //   return;
    // }

    // // Check if any PII field is empty
    // const emptyPiiField = Object.entries(piiValues).some(([id, value]) => {
    //   const pii = formData.pii_list.find((p) => p.customer_pii_label_id === Number(id));
    //   return pii && value.trim() === '';
    // });

    // if (emptyPiiField) {
    //   return;
    // }

    // Check if any required consent checkbox is unchecked
    const uncheckedRequiredCheckbox = Object.values(consentStatus).some((consent) => {
      return consent.compulsory_consent && !consent.consent_status;
    });

    if (uncheckedRequiredCheckbox) {
      toast.error(t('FrontEndErrorMessage.FormValidation.PleaseAgreeToAllRequiredConsents'));
      return;
    }

    const pii_data = Object.entries(piiValues).map(([id, value]) => {
      const pii = formData.pii_list.find((p) => p.pii_label_id === Number(id));
      return {
        pii_label_id: pii?.pii_label_id,
        pii_label_name: pii?.pii_label_name,
        pii_value: value,
      };
    });

    const payload = {
      customer_id: formData.additional_info.customer_id,
      collection_template_id: formData.additional_info.collection_template_id,
      consent_source: 'web_form',
      geolocation: '',
      continent: '',
      subject_identity_id: formData.additional_info.subject_identity_type_id,
      subject_identity_value:
        pii_data.find((pii) => pii.pii_label_id === subjectIdentityData?.id)?.pii_value || '',
      subject_identity_name: formData.additional_info.subject_identity_type_name,
      verification_key: formData.additional_info.template_verfication_key,
      pii_data,
      consent_status: Array.from(consentStatus.values()).flat(),
    };

    try {
      toast.dismiss();
      toast.loading(t('CommonErrorMessages.Processing'));
      const response = await axios.post(`${baseURL}/api/v2/data-collector/records`, payload);
      console.log('Response:', response.data);
      if (response.data?.success) {
        toast.dismiss();
        toast.success(t('CommonErrorMessages.FormSubmittedSuccessfully'));
        setIsModalOpen(false);
        setTimeout(() => {
          window.location.href = 'http://192.168.125.105:8081/Home/thank-you';
        }, 1500); // Small delay to allow success toast
      }
      // Clear otpVerified from localStorage
      // localStorage.removeItem('otpVerified');
      //Convert consentStatus to JSON or another format suitable for URL

      //! below code is for redirection
      //const consentDetailsParam = encodeURIComponent(JSON.stringify(consentStatus));

      // Open the new URL in a new tab
      // window.open(`${formRedirectURL}?consent_details=${consentDetailsParam}`, '_blank');
    } catch (error) {
      console.error('Error submitting form 1:', error);
    }
  };

  // if (isLoading) {
  //   return (
  //     <div
  //       style={{
  //         display: 'flex',
  //         justifyContent: 'center',
  //         alignItems: 'center',
  //         height: '100%',
  //         width: '100%',
  //         fontSize: '14px',
  //         color: '#000',
  //         fontWeight: 'bold',
  //         textAlign: 'center',
  //       }}
  //     >
  //       <Spinner />
  //     </div>
  //   );
  // }

  // if (hasError) {
  //   return (
  //     <main
  //       className="h-[100vh] w-full"
  //       style={{
  //         display: 'flex',
  //         flexDirection: 'column',
  //         alignItems: 'center',
  //         justifyContent: 'center',
  //         gap: '10px',
  //         textAlign: 'center',
  //       }}
  //     >
  //       <p style={{ color: '#FF0000', fontSize: '14px', fontWeight: 'bold' }}>
  //         An error occurred while loading the form.
  //       </p>
  //       <Button onClick={() => window.location.reload()} variant="secondary">
  //         Retry
  //       </Button>
  //     </main>
  //   );
  // }

  return (
    <ReactModal
      isOpen={isModalOpen}
      onRequestClose={() => setIsModalOpen(false)}
      style={{
        content: {
          width: '1000px', // Set a fixed width instead of maxWidth
          height: '730px', // Auto height based on content
          position: 'absolute', // 🔑 Important for centering
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          borderRadius: '12px',
          padding: '0',
          border: 'none',
          overflow: 'auto', // In case content is long
        },
        overlay: {
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          zIndex: 1000,
        },
      }}
    >
      {isLoading ? (
        <div className="flex h-full w-full items-center justify-center">
          <Spinner />
        </div>
      ) : hasError ? (
        <div className="flex h-full w-full flex-col items-center justify-center gap-4 p-8 text-center">
          <p className="text-sm font-bold text-red-500">
            An error occurred while loading the form.
          </p>
          <Button onClick={() => window.location.reload()} variant="secondary">
            Retry
          </Button>
        </div>
      ) : (
        <main className="flex h-full w-full flex-col gap-2 px-8 py-5">
          <header className="flex h-fit w-full flex-row items-center justify-between gap-2">
            <img
              src={formData?.form_configuration?.logoUrl}
              alt="Logo"
              className="h-[60px] w-[60px] object-contain"
            />
            <h2 className="text-lg font-semibold text-gray-500">
              {formData?.basic_info?.preference_input_heading ??
                formData?.form_configuration?.sectionTwoHeading ??
                staticData?.heading2}
            </h2>
            <button
              onClick={() => setIsModalOpen(false)}
              className="text-gray-500 hover:text-black"
            >
              ✕
            </button>
          </header>
          <hr className="border-1 border-grey-200 w-full border" />
          <section
            style={{
              display: 'flex',
              height: '100%',
              width: '100%',
              justifyContent: 'space-between',
              fontFamily: formData?.form_configuration?.fontFamily,
              fontSize,
            }}
            className="sm:text-sm md:text-base"
          >
            <section className="h-fit">
              <div
                style={{ display: 'flex', flexDirection: 'column', gap: '8px', width: '100%' }}
                className="h-fit"
              >
                {formData?.purpose_list.map((purpose) => (
                  <div
                    key={purpose.processing_purpose_id}
                    style={{
                      width: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '4px',
                    }}
                  >
                    <p className="font-medium">{purpose.processing_purpose_name}</p>
                    <div>
                      {purpose.consent_bucket.map((consent) => (
                        <div
                          key={consent.consent_purpose_id}
                          className={`mt-2 grid w-full items-start gap-4 ${
                            consent.ct_cp_map_bucket[0]?.frequency
                              ? 'grid-cols-[4fr_1fr]'
                              : 'grid-cols-1'
                          }`}
                        >
                          <div className="flex w-full flex-row items-start gap-2">
                            <div className="flex flex-row items-center gap-2">
                              <Checkbox
                                style={{
                                  height: '16px',
                                  width: '16px',
                                  border: '1px solid #ccc',
                                  marginTop: '2px',
                                  backgroundColor: consentStatus?.get(
                                    consent?.consent_purpose_id
                                  )?.[0]?.consent_status
                                    ? formData?.form_configuration?.backgroundHeaderColor
                                    : 'transparent',
                                }}
                                defaultChecked={
                                  consentStatus?.get(consent?.consent_purpose_id)?.[0]
                                    ?.consent_status
                                    ? true
                                    : consent.ct_cp_map_bucket[0]?.default_opt_out_allowed
                                      ? true
                                      : false
                                }
                                checked={
                                  consentStatus?.get(consent?.consent_purpose_id)?.[0]
                                    ?.consent_status
                                }
                                id={consent?.consent_purpose_id?.toString()}
                                onCheckedChange={(value: boolean) =>
                                  handleConsentChange(consent.consent_purpose_id, value)
                                }
                                required={consent.ct_cp_map_bucket[0]?.compulsory_consent}
                                disabled={
                                  consent.ct_cp_map_bucket[0]?.default_opt_out_allowed &&
                                  consent.ct_cp_map_bucket[0]?.compulsory_consent
                                }
                                className="focus-visible:ring-0 focus-visible:ring-offset-0 aria-checked:bg-primary aria-checked:focus:ring-0"
                              />
                            </div>

                            <div className="flex flex-col flex-wrap gap-2">
                              <p className="flex flex-col gap-2">
                                <p className="font-medium">
                                  {consent.consent_purpose_name}
                                  {consent?.ct_cp_map_bucket[0]?.compulsory_consent && (
                                    <span className="ml-1 text-red-500">*</span>
                                  )}
                                </p>
                                <p>{consent.consent_purpose_description}</p>
                              </p>
                              {/* PII Labels */}
                              {consent?.pii_labels?.length > 0 && (
                                <p className="flex w-full flex-row flex-wrap gap-2">
                                  <p>PII:</p>
                                  {consent?.pii_labels?.map((consent) => {
                                    if (consent) {
                                      return (
                                        <Badge
                                          className={badgeClass}
                                          style={{
                                            backgroundColor:
                                              formData?.form_configuration?.backgroundHeaderColor,
                                          }}
                                        >
                                          {consent}
                                        </Badge>
                                      );
                                    }
                                    return <></>;
                                  })}
                                </p>
                              )}
                              {/* Vendors */}
                              {consent?.vendors?.length > 0 && (
                                <p className="flex w-full flex-row flex-wrap gap-2">
                                  <p>Vendor:</p>
                                  {consent?.vendors?.map((consent) => {
                                    if (consent) {
                                      return (
                                        <Badge
                                          className={badgeClass}
                                          style={{
                                            backgroundColor:
                                              formData?.form_configuration?.backgroundHeaderColor,
                                          }}
                                        >
                                          {consent}
                                        </Badge>
                                      );
                                    }
                                    return <></>;
                                  })}
                                </p>
                              )}
                              {/* Processes */}
                              {consent?.processes?.length > 0 && (
                                <p className="flex w-full flex-row flex-wrap gap-2">
                                  <p>Process:</p>
                                  {consent?.processes?.map((consent) => {
                                    if (consent) {
                                      return (
                                        <Badge
                                          className={badgeClass}
                                          style={{
                                            backgroundColor:
                                              formData?.form_configuration?.backgroundHeaderColor,
                                          }}
                                        >
                                          {consent}
                                        </Badge>
                                      );
                                    }
                                    return <></>;
                                  })}
                                </p>
                              )}
                            </div>
                          </div>

                          {consent.ct_cp_map_bucket[0]?.frequency && (
                            <div
                              style={{
                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',
                                gap: '8px',
                              }}
                              className="w-full pr-2"
                            >
                              <Label
                                style={{
                                  width: 'auto',
                                }}
                              >
                                {staticData?.frequency}:
                              </Label>
                              {/* Frequency */}
                              <div className="w-full">
                                <Select
                                  defaultValue={consent.ct_cp_map_bucket[0].frequency?.toUpperCase()}
                                  onValueChange={(value) =>
                                    handleFrequencyChange(
                                      consent.consent_purpose_id,
                                      value?.toLowerCase()
                                    )
                                  }
                                >
                                  <SelectTrigger
                                    style={{
                                      width: '100%',
                                      fontFamily: formData?.form_configuration?.fontFamily,
                                    }}
                                  >
                                    <SelectValue placeholder="Select Frequency" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectGroup>
                                      <SelectLabel>Frequency</SelectLabel>
                                      {frequencyList.map((option) => (
                                        <SelectItem value={option.key} key={option.key}>
                                          {option.name}
                                        </SelectItem>
                                      ))}
                                    </SelectGroup>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              <p
                style={{
                  marginTop: '20px',
                  width: '100%',
                  textAlign: 'justify',
                }}
                className="h-fit"
              >
                By submitting this form, I agree to all{' '}
                <a
                  href="https://www.honda2wheelersindia.com/terms-and-conditions"
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    color: formData?.form_configuration?.backgroundHeaderColor,
                    textDecoration: 'underline',
                  }}
                >
                  terms and conditions
                </a>
                . Additionally, I consent to Honda collecting, using, and sharing my personal and
                sensitive information in accordance with{' '}
                <a
                  href="https://www.honda2wheelersindia.com/privacy-policy"
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    color: formData?.form_configuration?.backgroundHeaderColor,
                    textDecoration: 'underline',
                  }}
                >
                  Honda's Data Privacy Policy
                </a>
                . This policy upholds the highest standards of privacy and governance, as outlined
                on the website.
              </p>

              <p className="mt-2">
                If you wish to withdraw your consent at any time, please{' '}
                <a
                  href="https://sandbox.gotrust.tech/pf?customer_id=520&pf_id=26"
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    color: formData?.form_configuration?.backgroundHeaderColor,
                    textDecoration: 'underline',
                  }}
                >
                  click here to withdraw
                </a>
                .
              </p>

              {/* <div style={{ display: 'flex', width: '100%', justifyContent: 'flex-end' }}>
                {(formData?.form_configuration?.showPrivacyNoticeUrl &&
                  formData?.form_configuration?.privacyNoticeUrl) ||
                formData?.additional_info?.privacy_notice_id ? (
                  <a
                    href={
                      formData?.form_configuration?.privacyNoticeUrl
                        ? formData.form_configuration.privacyNoticeUrl.startsWith('http')
                          ? formData.form_configuration.privacyNoticeUrl
                          : `https://${formData.form_configuration.privacyNoticeUrl}`
                        : `${import.meta.env.VITE_APP_FRONTEND_URL}/notice/${formData.additional_info?.privacy_notice_id}?customer_id=${formData.additional_info?.customer_id}`
                    }
                    className="mr-auto mt-2 text-blue-500 underline"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Privacy Notice
                  </a>
                ) : null}
              </div> */}
            </section>
            {formData?.form_configuration?.showLanguage && (
              <div className="flex w-fit flex-row">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Globe className="mr-2 cursor-pointer" />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="h-auto w-56 overflow-auto font-primary-text">
                    <DropdownMenuLabel>Languages</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuRadioGroup
                      value={selectedLanguage}
                      // onValueChange={(value: string) => handleLanguageChange(value)}
                      onValueChange={(value: string) => setSelectedLanguage(value)}
                    >
                      <DropdownMenuRadioItem value="en" key="en">
                        English
                      </DropdownMenuRadioItem>
                      {languages?.map((lang: { language_code: string; language: string }) => (
                        <DropdownMenuRadioItem
                          value={lang?.language_code}
                          key={lang?.language_code}
                        >
                          {lang?.language}
                        </DropdownMenuRadioItem>
                      ))}

                      {/* {languages?.length === 0 && (
                    <DropdownMenuRadioItem disabled={true} value="-1">
                      No translated languages available
                    </DropdownMenuRadioItem>
                  )} */}
                      {/* <DropdownMenuRadioItem value="en">English</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="hi">Hindi</DropdownMenuRadioItem> */}
                    </DropdownMenuRadioGroup>
                  </DropdownMenuContent>
                </DropdownMenu>
                <span>{selectedLanguage?.toUpperCase()}</span>
              </div>
            )}
          </section>
          <hr className="border-1 border-grey-200 w-full border" />
          <footer className="mt-2 flex w-full flex-row items-center justify-end gap-2">
            <Button
              className="w-[120px] rounded-none bg-[#333] text-white"
              style={{
                fontFamily: formData?.form_configuration?.fontFamily,
              }}
              onClick={() => setIsModalOpen(false)}
            >
              CANCEL
            </Button>
            <Button
              className="w-[120px] rounded-none bg-[#333] text-white"
              style={{
                fontFamily: formData?.form_configuration?.fontFamily,
              }}
              onClick={() => submitForm()}
            >
              SUBMIT
            </Button>
          </footer>
        </main>
      )}
    </ReactModal>
  );
};

export default ConsentCheckbox;
