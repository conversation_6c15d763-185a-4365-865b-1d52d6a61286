import axios from 'axios';
import React, { ChangeEvent, useEffect, useMemo, useState } from 'react';

import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { Badge } from '../../@/components/ui/badge';
import { Button } from '../../@/components/ui/Common/Elements/Button/ButtonSort';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '../../@/components/ui/Common/Elements/Select/Select';
import { Dialog, DialogContent } from '../../@/components/ui/dialog';
import { get_supported_languages } from '../../components/common/services/universal-consent-management';
import ShadcnDialog from '../../components/common/shadcn-dialog';
import Spinner from '../../components/common/spinner';
import {
  ConsentFormDataType,
  ConsentFormFrequencyProperties,
  CTCPMapBucket,
  DataForVerificationProperties,
  FormConfiguration,
  PiiValuesType,
  ResponseType,
  StaticDataType,
  SubjectIdentityProperties,
} from '../../types/universal-consent-management';
import styles from './VerifyInputModal.module.css';
// import { get_frequency } from '../../common/services/universal-consent-management';

// Type for PII input values (e.g., First Name, Last Name)

const UCMForm = () => {
  const { t } = useTranslation();
  let verificationTab: Window | null = null;
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const SECRET_KEY = 'A!d9f@Y7s#pLk2vX$M1nE5qT&jRzUw3*';
  const badgeClass = 'text-[white] bg-primary';
  const [contentId, setContentId] = useState<string | null>(queryParams.get('source_id'));
  const [formData, setFormData] = useState<ConsentFormDataType | null>(null);
  //! for form validation set otp verified to false
  const [otpVerified, setOtpVerified] = useState<boolean>(false);
  const [staticData, setStaticData] = useState<StaticDataType>({
    heading1: 'Additional Personal Details',
    heading2: 'AGREEMENTS',
    frequency: 'Frequency',
    agreement_text: 'I agree to share my',
    for: 'for',
    footer_content:
      'By submitting this form, I agree to all terms and conditions. Additionally, I consent to the collection, use, and sharing of my personal and sensitive information by AEBC in accordance with GoTrust’s Data Privacy Policy, which upholds the highest standards of privacy and governance, as outlined on the website.',
    buttonText: 'Submit',
    formHeading: 'Consent Form',
  });
  const [frequencyList, setFrequencyList] = useState<ConsentFormFrequencyProperties[]>([]);
  const [piiValues, setPiiValues] = useState<PiiValuesType>({});
  const [consentStatus, setConsentStatus] = useState<Map<number, CTCPMapBucket[]>>(new Map());
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasError, setHasError] = useState<boolean>(false);
  const [ip, setIP] = useState('');
  const [countryCode, setCountryCode] = useState<string>('');
  const [continent, setContinent] = useState<string>('');
  const [userInput, setUserInput] = useState<string>();
  const [subjectIdentityData, setSubjectIdentityData] = useState<SubjectIdentityProperties>();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [isOtpVerifying, setIsOtpVerifying] = useState<boolean>(false);

  const [dialogTitle, setDialogTitle] = useState('');
  const [isOTPSend, setIsOTPSend] = useState<boolean>(false);
  const [otpVerificationDialog, setOtpVerificationDialog] = useState<boolean>(false);
  const [data, setData] = useState<DataForVerificationProperties>({
    id: formData?.additional_info?.customer_id,
    subject_identity: formData?.additional_info?.subject_identity_type_name,
    inputValue: '',
  });

  const [dialogDescription, setDialogDescription] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);
  const [formRedirectURL, setFormRedirectURL] = useState<string>('');
  const [selectedLanguage, setSelectedLanguage] = useState<string>('en');
  const [otp, setOtp] = useState(new Array(6).fill(''));
  const [languages, setLanguages] = useState([]);
  const [customerId, setCustomerId] = useState<number>();
  const [formId, setFormId] = useState<number>();
  const [formConfig, setFormConfig] = useState<FormConfiguration>();
  const scriptElement = document.querySelector('script[src*="consent-form-bundle.js"]');
  let fontSize = '';

  const allConsents = Array.from(consentStatus.values()).flat();
  const allChecked =
    allConsents.length > 0 &&
    allConsents.every((consent) => consent.consent_status || consent.compulsory_consent);

  // fetching ip and country code
  useEffect(() => {
    const getData = async () => {
      const response = await axios.get('https://api.ipify.org/?format=json');
      setIP(response.data.ip);
    };

    const getCountry = async () => {
      const response = await axios.get(
        'https://api.ipdata.co?api-key=295ad47616f5cc86434a4a501a2b9dba7eb6aeec63a70e09620aab10'
      );
      setCountryCode(response?.data?.country_code);
      setContinent(response?.data?.continent_name);
    };

    getData();
    getCountry();
  }, []);

  useEffect(() => {
    if (otpVerified) {
      submitForm();
    }
  }, [otpVerified]);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setHasError(false);
      try {
        const response = await axios.get<ResponseType>(
          `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v3/gtf/display?source_id=${contentId}${selectedLanguage ? `&language_code=${selectedLanguage}` : ''}`
        );
        const data = response.data;

        const initialPiiValues: PiiValuesType = {};

        data.result.data.pii_list.forEach((pii) => {
          initialPiiValues[pii.pii_label_id] = ''; // Default empty values for PII fields
        });

        data.result.data.purpose_list.forEach((purpose) => {
          purpose.consent_bucket.forEach((consent) => {
            setConsentStatus((prev) => {
              const updatedMap = new Map(prev);

              // Modify ct_cp_map_bucket to ensure default_opt_out_allowed items have consent_status as true
              const updatedBucket = consent.ct_cp_map_bucket.map((item) => ({
                ...item,
                consent_status: item.default_opt_out_allowed ? true : item.consent_status,
              }));

              updatedMap.set(consent.consent_purpose_id, updatedBucket);

              return updatedMap;
            });
          });
        });

        setSubjectIdentityData({
          id: data.result.data.additional_info.subject_identity_type_id,
          name: data.result.data.additional_info.subject_identity_type_name,
        });
        setPiiValues(initialPiiValues);

        setFormData(data.result.data);
        setFormConfig(data.result.data.form_configuration);
        setCustomerId(data.result.data.additional_info.customer_id);
        setFormId(data.result.data.additional_info.form_id);
        fontSize =
          data.result.data.form_configuration?.fontSize === 'small'
            ? '12px'
            : data.result.data.form_configuration?.fontSize === 'medium'
              ? '16px'
              : '20px';
        setFormRedirectURL(
          data?.result?.data?.additional_info?.form_redirect_url
            ? data?.result?.data?.additional_info?.form_redirect_url
            : ''
        );
      } catch (error) {
        console.error(error);
        setHasError(true);
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [contentId, selectedLanguage]);
  // fetching Supported Languages
  useEffect(() => {
    const fetchSupportedLanguages = async () => {
      try {
        // Call the returned function to fetch data
        const responseData = await get_supported_languages(
          'form',
          customerId,
          undefined,
          undefined,
          formId
        );
        setLanguages(responseData?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchSupportedLanguages();
  }, [customerId, formId]);

  //! below use effect are for form validation. Uncomment when need to use.

  useEffect(() => {
    // Check if OTP is already verified
    const isOtpVerified = localStorage.getItem('otpVerified') === 'true';
    setOtpVerified(isOtpVerified);

    // Listen for the localStorage event triggered by the verification tab
    const handleStorageEvent = (event: StorageEvent) => {
      if (event.key === 'otpVerified' && event.newValue === 'true') {
        setOtpVerified(true);
        toast.success(t('ToastMessages.Authentication.OTPVerifiedSuccessfully'));
      }
    };

    window.addEventListener('storage', handleStorageEvent);

    // Cleanup listener on component unmount
    return () => {
      window.removeEventListener('storage', handleStorageEvent);
    };
  }, []);

  useEffect(() => {
    // Clear otpVerified from localStorage when the page reloads
    const clearOtpVerified = () => {
      localStorage.removeItem('otpVerified');
    };

    window.addEventListener('beforeunload', clearOtpVerified);

    // Cleanup listener on component unmount
    return () => {
      window.removeEventListener('beforeunload', clearOtpVerified);
    };
  }, []);

  // fetching frequency
  useEffect(() => {
    const fetchFrequency = async () => {
      try {
        // Call the returned function to fetch data
        const response = await axios.get(
          `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v2/ct/frequency-list${selectedLanguage ? `?language_code=${selectedLanguage}` : ''}`
        );
        setFrequencyList(response?.data?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchFrequency();
  }, [contentId, selectedLanguage]);

  // Initialize `piiValues` dynamically when formData changes
  useEffect(() => {
    if (formData?.pii_list) {
      const initialPiiValues = formData.pii_list.reduce((acc, pii) => {
        acc[pii.pii_label_id] = ''; // Default empty values for all PII fields
        return acc;
      }, {} as PiiValuesType);
      setPiiValues(initialPiiValues);
    }
  }, [formData]);

  const handleSubmit = async () => {
    toast.loading(t('CommonErrorMessages.Sending'));
    // Extract the value of the input field dynamically
    const inputName = formData?.additional_info?.subject_identity_type_id?.toString() || '';

    const userSubjectIdentityInput = document.getElementById(inputName) as HTMLInputElement;

    const userSubjectIdentityValue = userSubjectIdentityInput
      ? userSubjectIdentityInput.value
      : null;

    const payload = {
      customer_id: formData?.additional_info?.customer_id,
      email_address: userSubjectIdentityValue,
    };

    try {
      const response = await axios.post(
        `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v1/gtf/otp/send`,
        payload
      );

      toast.dismiss();
      if (response.data.result.data.user_found === true) {
        toast.success(t('ToastMessages.Authentication.OTPSentSuccessfully'));
        setIsOTPSend(true);
        setOtpVerificationDialog(true);
        setOtp(new Array(6).fill('')); // Clear OTP input values
        setData({
          id: formData?.additional_info?.customer_id,
          subject_identity: formData?.additional_info?.subject_identity_type_name,
          inputValue: userSubjectIdentityValue,
        });
        setUserInput(userSubjectIdentityValue as string);
        openVerificationTab();
      } else if (response.data.result.data.user_found === false) {
        // setOtpVerified(true);
        submitForm();
      }
    } catch (error) {
      toast.dismiss();
      console.error('Error submitting form:', error);
      toast.error(t('FrontEndErrorMessage.Authentication.FailedToSendOTP'));
    }
  };
  const [shouldSubmit, setShouldSubmit] = useState(false);

  const handleOtpSubmit = async () => {
    toast.dismiss();
    // Join the OTP array into a single string
    const otpCode = otp.join('');
    // Check if OTP is provided and is exactly 6 digits
    if (!otpCode || otpCode.length !== 6) {
      toast.error('Please enter a valid 6-digit OTP');
      return;
    }
    setIsOtpVerifying(true);
    const payload = {
      otp: otpCode,
      email_address: userInput,
    };
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v1/pc/otp/verify`,
        payload
      );
      if (response.status === 200) {
        toast.success('OTP verified successfully!');
        setOtpVerificationDialog(false);
        setOtpVerified(true);
        localStorage.setItem('otpVerified', 'true'); // Notify main tab
        window.close(); // Close this tab
      }
    } catch (error) {
      console.error('OTP verification failed:', error);
      toast.error('Invalid OTP');
    } finally {
      setIsOtpVerifying(false);
    }
  };

  useEffect(() => {
    if (shouldSubmit && handleOtpSubmit) {
      handleOtpSubmit();
      setShouldSubmit(false);
    }
  }, [otp, shouldSubmit, handleOtpSubmit]);

  const handlePasteOtp = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const paste = e.clipboardData.getData('text').trim();
    const isAllDigits = paste.split('').every((char) => char >= '0' && char <= '9');
    if (!isAllDigits || typeof otp?.length !== 'number') return;
    const otpArray = paste.split('').slice(0, otp.length);

    // Only proceed if we have the right number of digits
    if (otpArray.length === otp.length) {
      setOtp(otpArray);
      setShouldSubmit(true);
    } else {
      // If incomplete OTP, just set what we have
      setOtp(otpArray.concat(new Array(otp.length - otpArray.length).fill('')));
    }
  };
  const openVerificationTab = () => {
    // if (verificationTab && !verificationTab.closed) {
    // verificationTab.focus();
    // toast.error('Verification tab is already open.');
    // return;
    // }
    // const data: DataForVerificationProperties = {
    // id: formData?.additional_info?.customer_id,
    // subject_identity: formData?.additional_info?.subject_identity_type_name,
    // inputValue: '',
    // };
    // Extract the value of the input field dynamically
    // const inputName = formData?.additional_info?.subject_identity_type_id?.toString() || '';
    // const userSubjectIdentityInput = document.getElementById(inputName) as HTMLInputElement;
    // const userSubjectIdentityValue = userSubjectIdentityInput
    // ? userSubjectIdentityInput.value
    // : null;
    // data.inputValue = userSubjectIdentityValue;
    // Save data to sessionStorage
    // sessionStorage.setItem('verificationData', JSON.stringify(data));
    // localStorage.setItem('otpVerified', 'false');
    // Encrypt the data
    // const encryptedData = CryptoJS.AES.encrypt(JSON.stringify(data), SECRET_KEY).toString();
    // verificationTab = window.open(
    // `${window.location.origin}/consent-form/verification?data=${encodeURIComponent(encryptedData)}`
    // );
    // if (!verificationTab) {
    // toast.error('Failed to open a new tab. Please allow pop-ups in your browser.');
    // return;
    // }
    // const checkTabClosed = setInterval(() => {
    // if (verificationTab?.closed) {
    // clearInterval(checkTabClosed);
    // if (!localStorage.getItem('otpVerified')) {
    // toast.dismiss();
    // toast.error('Verification was not completed. Please try again.');
    // }
    // }
    // }, 500);
  };

  const translateFormData = (formData: any, translationMap: Record<string, string>): any => {
    // Recursive function to traverse and update formData
    const deepTranslate = (data: any): any => {
      if (Array.isArray(data)) {
        // Iterate over array elements
        return data.map((item) => deepTranslate(item));
      } else if (typeof data === 'object' && data !== null) {
        // Iterate over object keys
        const updatedObject: Record<string, any> = {};
        for (const key in data) {
          if (Object.prototype.hasOwnProperty.call(data, key)) {
            const value = data[key];

            // Check if value matches a source key in the translation map
            if (typeof value === 'string' && translationMap[value]) {
              updatedObject[key] = translationMap[value];
            } else {
              // Recursively translate nested objects or arrays
              updatedObject[key] = deepTranslate(value);
            }
          }
        }
        return updatedObject;
      }
      // Return primitive values as is
      return data;
    };

    // Translate the entire formData structure
    return deepTranslate(formData);
  };

  //! for sending api request to bhashini for language change
  // const handleLanguageChange = async (value: string) => {
  //   if (!formData) {
  //     toast.error('Form data is not available for translation.');
  //     return;
  //   }

  //   setIsLoading(true); // Start the loader

  //   // Extract dynamic values to be translated
  //   const extractValues = (data: any): string[] => {
  //     const values: string[] = [];

  //     const traverse = (obj: any) => {
  //       if (Array.isArray(obj)) {
  //         obj.forEach((item) => traverse(item));
  //       } else if (typeof obj === 'object' && obj !== null) {
  //         Object.values(obj).forEach((value) => traverse(value));
  //       } else if (typeof obj === 'string') {
  //         values.push(obj);
  //       }
  //     };

  //     traverse(data);
  //     return values;
  //   };

  //   // Extract values from both formData and staticData
  //   const formDataValues = extractValues(formData);
  //   const staticDataValues = extractValues(staticData);

  //   const sourceTexts = [...formDataValues, ...staticDataValues];

  //   // Prepare the payload for translation
  //   const payload = {
  //     pipelineTasks: [
  //       {
  //         taskType: 'translation',
  //         config: {
  //           language: {
  //             sourceLanguage: selectedLanguage,
  //             targetLanguage: value,
  //           },
  //           serviceId: nmt_service_id,
  //         },
  //       },
  //     ],
  //     inputData: {
  //       input: sourceTexts.map((text) => ({ source: text })),
  //     },
  //   };

  //   try {
  //     const response = await axios.post(`${callback_url}`, payload, {
  //       headers: {
  //         Authorization: compute_call_authorization_value,
  //         'Content-Type': 'application/json',
  //       },
  //     });

  //     const original = response?.data?.pipelineResponse[0]?.output?.map(
  //       (item: any) => item?.source
  //     );
  //     const translation = response?.data?.pipelineResponse[0]?.output?.map(
  //       (item: any) => item?.target
  //     );

  //     const translationMap = original.reduce(
  //       (map: Record<string, string>, item: string, index: number) => {
  //         map[item] = translation[index];
  //         return map;
  //       },
  //       {}
  //     );

  //     // Translate the formData using the translationMap
  //     const updatedFormData = translateFormData(formData, translationMap);

  //     // Translate staticData
  //     const updatedStaticData = Object.keys(staticData).reduce((acc, key) => {
  //       acc[key] = translationMap[staticData[key]] || staticData[key];
  //       return acc;
  //     }, {} as StaticDataType);

  //     // Update states with translated data
  //     setFormData(updatedFormData);
  //     setStaticData(updatedStaticData);
  //     setSelectedLanguage(value);

  //   } catch (error) {
  //     console.error('Error translating form data:', error);
  //     toast.error('Failed to translate form data. Please try again.');
  //   } finally {
  //     setIsLoading(false); // Stop the loader
  //   }
  // };

  const isRequired = (piiLabelId: number) => {
    if (!formData?.pii_list) return;
    return formData.purpose_list?.some((purpose) =>
      purpose?.consent_bucket?.some((bucket) =>
        bucket?.ct_cp_map_bucket?.some(
          (mapBucket) =>
            mapBucket?.consent_status === true && piiLabelId === mapBucket?.pii_label_id
        )
      )
    );
  };

  const piiArray = useMemo(() => {
    if (!formData?.pii_list) return [];
    const requiredPiiIds: number[] = [];

    formData.pii_list.forEach((piiLabel) => {
      if (isRequired(piiLabel.pii_label_id)) {
        requiredPiiIds.push(piiLabel.pii_label_id);
      }
    });

    return requiredPiiIds;
  }, [formData]);
  const areAllFieldsFilled = useMemo(() => {
    if (!formData?.pii_list || piiArray.length === 0) {
      if (
        formData?.additional_info?.subject_identity_type_id !== undefined &&
        piiValues[formData.additional_info.subject_identity_type_id]?.trim().length > 0
      ) {
        return true;
      }
      return false;
    }
    return piiArray.every((pii) => piiValues[pii]?.trim().length > 0);
  }, [formData, piiValues, piiArray]);
  const handlePiiChange = (id: number, value: string) => {
    setPiiValues((prev) => ({ ...prev, [id]: value }));
  };
  const handleConsentChange = (consentId: number, checked: boolean) => {
    setConsentStatus((prev) => {
      // Clone the exi  ting Map (React state requires immutability)
      const updatedMap = new Map(prev);
      let updatedValue: CTCPMapBucket[] | undefined = updatedMap?.get(consentId);

      if (updatedValue) {
        updatedValue = updatedValue.map((item) => ({
          ...item,
          consent_status: checked,
        }));

        updatedMap.set(consentId, updatedValue);
      }
      // Update existing entry OR add a new one

      return updatedMap; // Update state with new Map
    });
  };
  const handleResendOtp = async () => {
    toast.dismiss();
    const payload = {
      customer_id: data?.id,
      email_address: userInput,
    };
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v1/pc/otp/send`,
        payload
      );
      toast.success('OTP resent successfully');
      setOtp(new Array(6).fill(''));
    } catch (error) {
      console.error('Error resending OTP:', error);
      toast.error('Failed to resend OTP. Please try again.');
    }
  };
  const handleChange = (element: HTMLInputElement, index: number) => {
    if (isNaN(Number(element.value))) return;
    setOtp([...otp.map((d, idx) => (idx === index ? element.value : d))]);
    // Move to the next input if the current input has a value
    const nextSibling = element.nextElementSibling as HTMLInputElement | null;
    if (element.value && nextSibling) {
      nextSibling.focus();
    }
  };
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (event.key === 'Backspace' && !otp[index] && index > 0) {
      setOtp([...otp.map((d, idx) => (idx === index - 1 ? '' : d))]);
      // Move to the previous input if it exists
      const previousSibling = event.currentTarget.previousElementSibling as HTMLInputElement | null;
      if (previousSibling) {
        previousSibling.focus();
      }
    }
  };

  const handleFrequencyChange = (consentId: number, newFrequency: string) => {
    // setConsentFrequency((prev) => ({
    //   ...prev,
    //   [consentId]: newFrequency,
    // }));

    // Also update the frequency in consentStatus
    setConsentStatus((prev) => {
      // Clone the existing Map (React state requires immutability)
      const updatedMap = new Map(prev);
      let updatedValue: CTCPMapBucket[] | undefined = updatedMap?.get(consentId);

      if (updatedValue) {
        updatedValue = updatedValue.map((item) => ({
          ...item,
          frequency: newFrequency?.toLowerCase(),
        }));

        updatedMap.set(consentId, updatedValue);
      }
      // Update existing entry OR add a new one

      return updatedMap; // Update state with new Map
    });
  };

  const handleConsentChangeAll = (checked: boolean) => {
    setConsentStatus((prev) => {
      const updatedMap = new Map<number, CTCPMapBucket[]>();
      // For every consent purpose (key) in your Map, update the consent_status for each bucket entry.
      prev.forEach((consentList, consentId) => {
        const updatedConsentList = consentList.map((item) => {
          if (!item.compulsory_consent) {
            return {
              ...item,
              consent_status: checked,
            };
          } else {
            return item;
          }
        });
        updatedMap.set(consentId, updatedConsentList);
      });
      return updatedMap;
    });
  };

  const submitForm = async () => {
    toast.dismiss();
    if (!formData) return;

    if (!areAllFieldsFilled) {
      toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.PleaseEnterAllFields'));
      return;
    }

    // Check if all required consent checkboxes are checked
    const allRequiredChecked = Array.from(consentStatus.values()).every((consentList) =>
      consentList.every((consent) =>
        consent.compulsory_consent ? consent.consent_status === true : true
      )
    );

    if (!allRequiredChecked) {
      toast.dismiss();
      toast.error(t('FrontEndErrorMessage.FormValidation.PleaseAgreeToAllRequiredConsents'));
      return;
    }

    // if (!otpVerified) {
    //   toast.error('Please verify OTP before submitting.');
    //   return;
    // }

    // // Check if any PII field is empty
    // const emptyPiiField = Object.entries(piiValues).some(([id, value]) => {
    //   const pii = formData.pii_list.find((p) => p.customer_pii_label_id === Number(id));
    //   return pii && value.trim() === '';
    // });

    // if (emptyPiiField) {
    //   toast.error('Please fill in all required fields.');
    //   return;
    // }

    // Check if any required consent checkbox is unchecked
    const uncheckedRequiredCheckbox = Object.values(consentStatus).some((consent) => {
      return consent.compulsory_consent && !consent.consent_status;
    });

    if (uncheckedRequiredCheckbox) {
      toast.error(t('FrontEndErrorMessage.FormValidation.PleaseAgreeToAllRequiredConsents'));
      return;
    }

    const pii_data = Object.entries(piiValues).map(([id, value]) => {
      const pii = formData.pii_list.find((p) => p.pii_label_id === Number(id));
      return {
        pii_label_id: pii?.pii_label_id,
        pii_label_name: pii?.pii_label_name,
        pii_value: value,
      };
    });

    const payload = {
      customer_id: formData.additional_info.customer_id,
      collection_template_id: formData.additional_info.collection_template_id,
      consent_source: 'web_form',
      geolocation: `${ip}[${countryCode}]`,
      continent,
      subject_identity_id: formData.additional_info.subject_identity_type_id,
      subject_identity_value:
        pii_data.find((pii) => pii.pii_label_id === subjectIdentityData?.id)?.pii_value || '',
      subject_identity_name: formData.additional_info.subject_identity_type_name,
      verification_key: formData.additional_info.template_verfication_key,
      pii_data,
      consent_status: Array.from(consentStatus.values()).flat(),
    };

    try {
      toast.dismiss();
      toast.loading(t('CommonErrorMessages.ProcessingEllipsis'));
      const response = await axios.post(
        `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v2/data-collector/records`,
        payload
      );
      toast.dismiss();
      if (response.data?.success) {
        setDialogTitle('Form submitted successfully!');
        setDialogDescription('Form submitted successfully!');
        setIsSuccess(true);
        setDialogOpen(true);
      }

      // Clear otpVerified from localStorage
      // localStorage.removeItem('otpVerified');
      //Convert consentStatus to JSON or another format suitable for URL

      //! below code is for redirection
      //const consentDetailsParam = encodeURIComponent(JSON.stringify(consentStatus));

      // Open the new URL in a new tab
      // window.open(`${formRedirectURL}?consent_details=${consentDetailsParam}`, '_blank');
    } catch (error:any) {
      toast.dismiss();
      toast.error(t(error?.response?.data?.message || error?.response?.data?.result?.error || "An error occured while submiting the form"));
      console.error('Error submitting form:', error);
      setDialogTitle('There was an error submitting the form. Please try again.');
      setDialogDescription('There was an error submitting the form. Please try again.');
      setIsSuccess(false);
      setDialogOpen(true);
    }
  };

  if (isLoading) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          width: '100%',
          fontSize: '14px',
          color: '#000',
          fontWeight: 'bold',
          textAlign: 'center',
        }}
      >
        <Spinner />
      </div>
    );
  }

  if (hasError) {
    return (
      <main
        className="h-[100vh] w-full"
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '10px',
          textAlign: 'center',
        }}
      >
        <p style={{ color: '#FF0000', fontSize: '14px', fontWeight: 'bold' }}>
          An error occurred while loading the form.
        </p>
        <Button onClick={() => window.location.reload()} variant="secondary">
          Retry
        </Button>
      </main>
    );
  }

  return (
    <>
      {otpVerificationDialog && (
        <Dialog
          open={otpVerificationDialog}
          onOpenChange={() => {
            setOtpVerificationDialog(false);
            setOtp(new Array(6).fill(''));
          }}
        >
          <DialogContent className="w-[800px]">
            <main className="flex h-full w-full items-center justify-center gap-5 rounded-lg">
              <main
                className={styles.otpMain}
                style={{
                  width: '200px',
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <header className={styles.otpHeader}>
                  <p>Verify your {data?.subject_identity}</p>
                  <p className={styles.otpSubText}>
                    We have sent a verification code to <strong>{`${userInput}`}</strong>
                  </p>
                </header>
                <form
                  onSubmit={(e) => {
                    e.preventDefault();
                    handleOtpSubmit();
                  }}
                  className={styles.otpInputContainer}
                >
                  {otp?.map((data, index) => (
                    <input
                      key={index}
                      type="text"
                      maxLength={1}
                      inputMode="numeric"
                      pattern="[0-9]"
                      value={data}
                      onChange={(e) => handleChange(e.target, index)}
                      onKeyDown={(e) => {
                        handleKeyDown(e, index);
                        // Add enter key handler
                        if (e.key === 'Enter' && index === otp.length - 1) {
                          e.preventDefault();
                          handleOtpSubmit();
                        }
                      }}
                      onPaste={index === 0 ? handlePasteOtp : undefined}
                      className="flex h-12 w-12 rounded border border-solid border-gray-300 bg-background px-3 py-2 text-center text-sm font-medium ring-offset-background placeholder:text-gray-400 focus:outline-none focus:ring-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    />
                  ))}
                </form>
                <footer className={styles.otpFooter}>
                  <button
                    type="submit"
                    className={styles.otpButton}
                    style={{
                      background: formConfig?.submit_button?.color,
                      fontSize: formConfig?.submit_button?.size,
                      opacity: isOtpVerifying ? 0.7 : 1,
                      cursor: isOtpVerifying ? 'not-allowed' : 'pointer',
                    }}
                    onClick={handleOtpSubmit}
                    disabled={isOtpVerifying}
                  >
                    {isOtpVerifying ? 'Verifying...' : formConfig?.submit_button?.text}
                  </button>
                  <button
                    type="button"
                    className={styles.otpResendButton}
                    style={{
                      color: formConfig?.submit_button?.color,
                      border: `1px solid ${formConfig?.submit_button?.color}`,
                      opacity: isOtpVerifying ? 0.7 : 1,
                      cursor: isOtpVerifying ? 'not-allowed' : 'pointer',
                    }}
                    onClick={handleResendOtp}
                    disabled={isOtpVerifying}
                  >
                    Resend OTP
                  </button>
                </footer>
              </main>
            </main>
          </DialogContent>
        </Dialog>
      )}
      <ShadcnDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        title={dialogTitle}
        // description={dialogDescription}
        dialogContentClassName="min-w-[250px] flex flex-col items-center justify-center text-center min-h-[100px]"
      />
      <main
        style={{
          height: '100vh',
          width: '100vw',
          fontFamily: formConfig?.form?.font_family,
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          padding: '16px',
          overflowY: 'auto',
        }}
      >
        <main
          style={{
            height: 'fit-content',
            width: 'fit-content',
            fontFamily: formConfig?.form?.font_family,
          }}
        >
          <section
            style={{
              height: '100%',
              width: '100%',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '8px',
                borderRadius: '8px',
                border: '1px solid #ccc',
                overflowY: 'auto',
                maxWidth: '100%',
                maxHeight: '90%',
                width: '100%',
                height: '100%',
                boxSizing: 'border-box',
              }}
            >
              <header
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '8px',
                  gap: '8px',
                  width: '100%',
                  height: 'fit-content',
                }}
              >
                {formConfig?.form?.logo?.show_logo && (
                  <img
                    src={formConfig?.form?.logo?.logo_url}
                    alt="Logo"
                    style={{
                      height: formConfig?.form?.logo?.height,
                      width: formConfig?.form?.logo?.width,
                      objectFit: 'contain',
                    }}
                  />
                )}
                <h2
                  style={{
                    fontSize: formConfig?.form?.heading?.size,
                    fontFamily: formConfig?.form?.font_family,
                    color: formConfig?.form?.heading?.color,
                    fontWeight: 'bold',
                  }}
                >
                  {formConfig?.form?.heading?.text}
                </h2>
                <div
                  style={{
                    display: 'flex',
                    width: 'fit-content',
                    flexDirection: 'row',
                    alignItems: 'flex-start',
                    gap: '8px' /* gap-2 = 8px */,
                  }}
                >
                  {formData?.form_configuration?.form?.show_language && (
                    <select
                      value={selectedLanguage}
                      onChange={(event: React.ChangeEvent<HTMLSelectElement>) =>
                        setSelectedLanguage(event.target.value)
                      }
                      style={{
                        height: '44px',
                        width: '124px',
                        border: '1px solid #d1d1d1',
                        backgroundColor: '#f3f4f6' /* bg-gray-100 */,
                        paddingLeft: '8px' /* px-2 = 8px */,
                        paddingRight: '8px',
                      }}
                    >
                      <option value="en" key="en">
                        English
                      </option>
                      {languages?.map((lang: { language_code: string; language: string }) => (
                        <option value={lang.language_code} key={lang.language_code}>
                          {lang.language}
                        </option>
                      ))}
                    </select>
                  )}
                  <button>✕</button>
                </div>
              </header>

              <hr
                style={{
                  height: '0.5px',
                  backgroundColor: '#ccc',
                  marginLeft: '20px',
                  marginRight: '20px',
                }}
              />

              <section
                style={{
                  paddingLeft: '16px',
                  paddingRight: '16px',
                }}
              >
                <div
                  style={{ display: 'flex', flexDirection: 'column', gap: '8px', width: '100%' }}
                >
                  <p
                    style={{
                      width: '100%',
                      textAlign: 'justify',
                      fontSize: formConfig?.form?.description?.size,
                      fontFamily: formConfig?.form?.font_family,
                      color: formConfig?.form?.description?.color,
                    }}
                  >
                    {formConfig?.form?.description?.text}
                  </p>
                </div>

                {/* <hr
                style={{
                  height: '0.5px',
                  backgroundColor: '#ccc',
                  margin: '10px',
                }}
              /> */}

                <div
                  style={{ display: 'flex', flexDirection: 'column', gap: '8px', width: '100%' }}
                >
                  {formConfig?.pii_section?.heading?.text && (
                    <p
                      style={{
                        fontWeight: 'bold',
                        fontSize: formConfig?.pii_section?.heading?.size,
                        fontFamily: formConfig?.form?.font_family,
                        color: formConfig?.pii_section?.heading?.color,
                      }}
                    >
                      {formConfig?.pii_section?.heading?.text}
                    </p>
                  )}
                  {formConfig?.pii_section?.description?.text && (
                    <p
                      style={{
                        fontSize: formConfig?.pii_section?.description?.size,
                        fontFamily: formConfig?.form?.font_family,
                        color: formConfig?.pii_section?.description?.color,
                        textAlign: 'justify',
                      }}
                    >
                      {formConfig?.pii_section?.description?.text}
                    </p>
                  )}
                  {formConfig?.pii_section?.show_pii_section && (
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '8px',
                        width: '100%',
                      }}
                    >
                      {formData?.pii_list.map((piiLabel) => {
                        return (
                          <div
                            key={piiLabel.pii_label_id}
                            style={{
                              display: 'flex',
                              flexDirection: 'row',
                              flexWrap: 'wrap',
                              alignItems: 'center',
                              gap: '16px',
                            }}
                          >
                            {formConfig?.pii_section?.show_labels && (
                              <label
                                style={{
                                  width: '200px',
                                  fontWeight: 'normal',
                                }}
                              >
                                {piiLabel.pii_label_name}{' '}
                                <span className="text-red-500">
                                  {piiLabel.pii_label_id ===
                                    formData.additional_info.subject_identity_type_id ||
                                  isRequired(piiLabel.pii_label_id)
                                    ? '* '
                                    : ''}
                                </span>
                                :{' '}
                              </label>
                            )}
                            <input
                              placeholder={`${piiLabel.pii_label_name}`}
                              style={{
                                border: '1px solid #ccc',
                                borderRadius: formConfig?.form?.border_radius,
                                padding: '8px',
                                height: '40px',
                                width: '200px',
                                fontWeight: 'normal',
                                fontFamily: formConfig?.form?.font_family,
                              }}
                              value={piiValues[piiLabel.pii_label_id]}
                              name={piiLabel?.pii_label_name}
                              id={piiLabel?.pii_label_id?.toString()}
                              onChange={(e) =>
                                handlePiiChange(
                                  piiLabel.pii_label_id,
                                  (e.target as HTMLInputElement).value
                                )
                              }
                              autoComplete="off"
                              required
                            />
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>

                {/* <hr
                style={{
                  height: '0.5px',
                  backgroundColor: '#ccc',
                  margin: '10px',
                }}
              /> */}

                <div
                  style={{ display: 'flex', flexDirection: 'column', gap: '8px', width: '100%' }}
                >
                  <p
                    style={{
                      fontWeight: 'bold',
                      fontSize: formConfig?.consent_collection_section?.heading?.size,
                      fontFamily: formConfig?.form?.font_family,
                      color: formConfig?.consent_collection_section?.heading?.color,
                    }}
                  >
                    {formConfig?.consent_collection_section?.heading?.text}
                  </p>
                  <p
                    style={{
                      fontSize: formConfig?.consent_collection_section?.description?.size,
                      fontFamily: formConfig?.form?.font_family,
                      color: formConfig?.consent_collection_section?.description?.color,
                    }}
                  >
                    {formConfig?.consent_collection_section?.description?.text}
                  </p>
                  {formConfig?.consent_collection_section?.show_check_all_checkbox ? (
                    <div>
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'start',
                          gap: '8px',
                        }}
                      >
                        <input
                          type="checkbox"
                          style={{
                            marginTop: '4px',
                            height: '18px',
                            width: '18px',
                            cursor: 'pointer',
                            accentColor: formConfig?.form?.color_scheme,
                            borderRadius: formConfig?.form?.border_radius,
                          }}
                          checked={allChecked}
                          onChange={(event: ChangeEvent<HTMLInputElement>) =>
                            handleConsentChangeAll(event.target.checked)
                          }
                        />
                        <p
                          style={{
                            fontFamily: formConfig?.form?.font_family,
                            fontSize: formConfig?.consent_collection_section?.description?.size,
                          }}
                        >
                          {formConfig?.consent_collection_section?.all_checkbox_text}
                        </p>
                      </div>
                      <ul style={{ listStyleType: 'disc', paddingLeft: '2.5rem' }}>
                        {formData?.purpose_list?.map((purpose) => (
                          <>
                            {/* Nested list for consent buckets */}
                            {purpose.consent_bucket.map((consent) => (
                              <li key={consent.consent_purpose_id} style={{ marginBottom: '1rem' }}>
                                {/* Wrap your flex layout in a child div */}
                                <div
                                  style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}
                                >
                                  <p style={{ margin: 0 }}>
                                    <strong>{consent.consent_purpose_name}</strong>
                                  </p>
                                  <p style={{ margin: 0 }}>{consent.consent_purpose_description}</p>
                                  {/* PII Labels */}
                                  {consent?.pii_labels?.length > 0 &&
                                    formConfig?.pii_section?.show_badges && (
                                      <div
                                        style={{
                                          display: 'flex',
                                          flexDirection: 'row',
                                          flexWrap: 'wrap',
                                          gap: '8px',
                                        }}
                                      >
                                        <span>PII:</span>
                                        {consent.pii_labels.map((label, index) =>
                                          label ? (
                                            <Badge
                                              key={index}
                                              style={{
                                                color: 'white',
                                                backgroundColor: formConfig?.form?.color_scheme,
                                              }}
                                            >
                                              {label}
                                            </Badge>
                                          ) : null
                                        )}
                                      </div>
                                    )}
                                  {/* Vendors */}
                                  {consent?.vendors?.length > 0 &&
                                    formConfig?.pii_section?.show_badges && (
                                      <div
                                        style={{
                                          display: 'flex',
                                          flexDirection: 'row',
                                          flexWrap: 'wrap',
                                          gap: '8px',
                                        }}
                                      >
                                        <span>Vendor:</span>
                                        {consent.vendors.map((vendor, index) =>
                                          vendor ? (
                                            <Badge
                                              key={index}
                                              style={{
                                                color: 'white',
                                                backgroundColor: formConfig?.form?.color_scheme,
                                              }}
                                            >
                                              {vendor}
                                            </Badge>
                                          ) : null
                                        )}
                                      </div>
                                    )}
                                  {/* Processes */}
                                  {consent?.processes?.length > 0 &&
                                    formConfig?.pii_section?.show_badges && (
                                      <div
                                        style={{
                                          display: 'flex',
                                          flexDirection: 'row',
                                          flexWrap: 'wrap',
                                          gap: '8px',
                                        }}
                                      >
                                        <span>Process:</span>
                                        {consent.processes.map((process, index) =>
                                          process ? (
                                            <Badge
                                              key={index}
                                              style={{
                                                color: 'white',
                                                backgroundColor: formConfig?.form?.color_scheme,
                                              }}
                                            >
                                              {process}
                                            </Badge>
                                          ) : null
                                        )}
                                      </div>
                                    )}
                                </div>
                              </li>
                            ))}
                          </>
                        ))}
                      </ul>
                    </div>
                  ) : (
                    <>
                      {formData?.purpose_list.map((purpose) => (
                        <div key={purpose.processing_purpose_id}>
                          {' '}
                          {purpose.consent_bucket.map((consent) => (
                            <div
                              key={consent.consent_purpose_id}
                              style={{
                                marginTop: '8px',
                                display: 'grid',
                                width: '100%',
                                alignItems: 'start',
                                gap: '16px',
                                gridTemplateColumns: consent.ct_cp_map_bucket[0]?.frequency
                                  ? '4fr 1fr'
                                  : '1fr',
                              }}
                            >
                              <div
                                style={{
                                  display: 'flex',
                                  flexDirection: 'row',
                                  width: '100%',
                                  alignItems: 'flex-start',
                                  gap: '8px',
                                }}
                              >
                                <div
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                  }}
                                >
                                  <input
                                    style={{
                                      height: '16px',
                                      width: '16px',
                                      marginTop: '2px',
                                      cursor:
                                        consent.ct_cp_map_bucket[0]?.default_opt_out_allowed &&
                                        consent.ct_cp_map_bucket[0]?.compulsory_consent
                                          ? 'not-allowed'
                                          : 'pointer',
                                      accentColor: formConfig?.form?.color_scheme,
                                      borderRadius: formConfig?.form?.border_radius,
                                    }}
                                    defaultChecked={
                                      consentStatus?.get(consent?.consent_purpose_id)?.[0]
                                        ?.consent_status
                                        ? true
                                        : consent.ct_cp_map_bucket[0]?.default_opt_out_allowed
                                          ? true
                                          : false
                                    }
                                    type="checkbox"
                                    checked={
                                      consentStatus?.get(consent?.consent_purpose_id)?.[0]
                                        ?.consent_status
                                    }
                                    onChange={(event: ChangeEvent<HTMLInputElement>) =>
                                      handleConsentChange(
                                        consent.consent_purpose_id,
                                        event.target.checked
                                      )
                                    }
                                    required={consent.ct_cp_map_bucket[0]?.compulsory_consent}
                                    disabled={
                                      consent.ct_cp_map_bucket[0]?.default_opt_out_allowed &&
                                      consent.ct_cp_map_bucket[0]?.compulsory_consent
                                    }
                                  />
                                  <label
                                    htmlFor={consent?.consent_purpose_id?.toString()}
                                    className="text-sm text-gray-700"
                                  >
                                    {consent?.ct_cp_map_bucket[0]?.compulsory_consent && (
                                      <span className="ml-1 text-red-500">*</span>
                                    )}
                                  </label>
                                </div>

                                <div className="flex flex-col flex-wrap gap-2">
                                  <p className="flex flex-col gap-2">
                                    <strong>{consent.consent_purpose_name}</strong>
                                    <p>{consent.consent_purpose_description}</p>
                                  </p>
                                  {/* PII Labels */}
                                  {consent?.pii_labels?.length > 0 &&
                                    formConfig?.pii_section?.show_badges && (
                                      <p className="flex w-full flex-row flex-wrap gap-2">
                                        <p>PII:</p>
                                        {consent?.pii_labels?.map((consent) => {
                                          if (consent) {
                                            return (
                                              <Badge
                                                style={{
                                                  color: 'white',
                                                  backgroundColor: formConfig?.form?.color_scheme,
                                                }}
                                              >
                                                {consent}
                                              </Badge>
                                            );
                                          }
                                          return <></>;
                                        })}
                                      </p>
                                    )}
                                  {/* Vendors */}
                                  {consent?.vendors?.length > 0 &&
                                    formConfig?.pii_section?.show_badges && (
                                      <p className="flex w-full flex-row flex-wrap gap-2">
                                        <p>Vendor:</p>
                                        {consent?.vendors?.map((consent) => {
                                          if (consent) {
                                            return (
                                              <Badge
                                                style={{
                                                  color: 'white',
                                                  backgroundColor: formConfig?.form?.color_scheme,
                                                }}
                                              >
                                                {consent}
                                              </Badge>
                                            );
                                          }
                                          return <></>;
                                        })}
                                      </p>
                                    )}
                                  {/* Processes */}
                                  {consent?.processes?.length > 0 &&
                                    formConfig?.pii_section?.show_badges && (
                                      <p className="flex w-full flex-row flex-wrap gap-2">
                                        <p>Process:</p>
                                        {consent?.processes?.map((consent) => {
                                          if (consent) {
                                            return (
                                              <Badge
                                                style={{
                                                  color: 'white',
                                                  backgroundColor: formConfig?.form?.color_scheme,
                                                }}
                                              >
                                                {consent}
                                              </Badge>
                                            );
                                          }
                                          return <></>;
                                        })}
                                      </p>
                                    )}
                                </div>
                              </div>

                              {consent.ct_cp_map_bucket[0]?.frequency && (
                                <div
                                  style={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    gap: '8px',
                                    width: '100%',
                                    paddingRight: '10px',
                                  }}
                                >
                                  <label
                                    style={{
                                      width: 'auto',
                                    }}
                                  >
                                    Frequency:
                                  </label>
                                  <div style={{ width: '100%' }}>
                                    <Select defaultValue={consent.ct_cp_map_bucket[0].frequency}>
                                      <SelectTrigger
                                        style={{
                                          width: '100%',
                                          fontFamily: formConfig?.form?.font_family,
                                        }}
                                      >
                                        <SelectValue placeholder="Select Frequency" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectGroup>
                                          <SelectLabel>Frequency</SelectLabel>
                                          {frequencyList.map((option) => (
                                            <SelectItem value={option.key} key={option.key}>
                                              {option.name}
                                            </SelectItem>
                                          ))}
                                        </SelectGroup>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      ))}
                    </>
                  )}
                </div>

                {formConfig?.form_footer?.heading?.text && (
                  <p
                    style={{
                      marginTop: '20px',
                      width: '100%',
                      fontSize: formConfig?.form_footer?.heading?.size,
                      fontFamily: formConfig?.form?.font_family,
                      color: formConfig?.form_footer?.heading?.color,
                      textAlign: 'justify',
                    }}
                  >
                    {formConfig?.form_footer?.heading?.text}
                  </p>
                )}

                {formConfig?.form_footer?.description?.text && (
                  <p
                    style={{
                      marginTop: '20px',
                      width: '100%',
                      fontSize: formConfig?.form_footer?.description?.size,
                      fontFamily: formConfig?.form?.font_family,
                      color: formConfig?.form_footer?.description?.color,
                      textAlign: 'justify',
                    }}
                  >
                    <div
                      dangerouslySetInnerHTML={{
                        __html: formConfig?.form_footer?.description?.text,
                      }}
                    ></div>
                  </p>
                )}
                <div
                  style={{
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                    gap: '10px',
                    marginBottom: '20px',
                  }}
                >
                  {formConfig?.form?.show_privacy_policy_url && (
                    <a
                      href={
                        formConfig?.form?.privacy_policy_url?.startsWith('http')
                          ? formConfig?.form?.privacy_policy_url
                          : `https://${formConfig?.form?.privacy_policy_url}`
                      }
                      style={{
                        fontSize: '14px',
                        fontFamily: formConfig?.form?.font_family,
                        color: formConfig?.form?.color_scheme,
                        textDecoration: 'underline',
                        marginRight: 'auto',
                        marginTop: '8px',
                      }}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {formConfig?.form?.privacy_policy_url_text}
                    </a>
                  )}
                  <button
                    style={{
                      fontFamily: formConfig?.form?.font_family,
                      fontSize: formConfig?.submit_button?.size,
                      background: formConfig?.submit_button?.color,
                      color: 'white',
                      marginTop: '10px',
                      borderRadius: formConfig?.form?.border_radius,
                      padding: '10px 20px',
                      width: 'fit-content',
                      height: 'fit-content',
                      cursor: `${!areAllFieldsFilled || otpVerified ? 'not-allowed' : 'pointer'}`,
                      opacity: `${!areAllFieldsFilled || otpVerified ? 0.5 : 1}`,
                    }}
                    onClick={handleSubmit}
                    disabled={!areAllFieldsFilled || otpVerified}
                  >
                    {formConfig?.submit_button?.text}
                  </button>
                </div>
              </section>
            </div>
          </section>
        </main>
      </main>
    </>
  );
};

export default UCMForm;
