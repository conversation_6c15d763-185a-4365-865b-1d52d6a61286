import { Option } from '../@/components/ui/MultipleSelector';

export interface TableHeading {
  id: string;
  label: string;
  sort: boolean;
  width: string;
}

export interface PrivacySection {
  id: string;
  title: string;
  content: string;
}
export interface StatsDivProperties {
  data: {
    title: string;
    value: string | number | undefined;
    trend?: {
      value: number;
      isPositive: boolean;
    };
    icon?: React.ReactNode;
    color?: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'normal';
  };
  loading?: boolean;
  className?: string;
}

export type DomainTableData = {
  tableHeadings?: TableHeading[];
  data: RecordTableRowData[];
  loading: boolean;
};

export type RecordTableRowData = {
  identity: string;
  Geolocation: string;
  consent_source: string;
  processing_purpose: string;
};
export type ConsentListTableRowData = {
  id: string;
  subject_name: string;
  source: string;
  status: string;
  last_modified: string;
  phone: string;
  created_at: string;
};
export type SubjectConsentListTypesTableData = {
  tableHeadings?: TableHeading[];
  data: ConsentListTableRowData[];
  loading: boolean;
};

export type TypesTableRowData = {
  subject_name: string;
  email: boolean;
  sms: boolean;
  mail: boolean;
  notification: boolean;
  frequency: string;
};
export type TypesTableData = {
  tableHeadings?: TableHeading[];
  data: TypesTableRowData[];
  loading: boolean;
};

export type TemplateTableTableData = {
  tableHeadings?: TableHeading[];
  data: TemplateTableRowData[];
  loading: boolean;
};
export type TemplateTableRowData = {
  name: string;
  status: string;
  type: string;
  owner: string;
  owner_email: string;
  entity: string;
  last_modified: string;
  created_at: string;
};
//CONSENT UPLOAD
export interface ConsentUploadPayment {
  job_id: string;
  description: string;
  status: 'Completed' | 'Pending';
  created_on: string;
  owner: string;
}
//ACTION DIALOG
export type actionDialogPayment = {
  name: string;
  description: string;
  status: string;
};

export interface EditDialogProperties {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  rowData: actionDialogPayment;
  setParameterData: React.Dispatch<React.SetStateAction<actionDialogPayment[]>>;
  parameterData: actionDialogPayment[];
}
//CUSTOM PARAMETER FORM
export type ConsentParamaterFormInputs = {
  name: string;
  description: string;
  status: string;
};

export interface CustomParameterFormData {
  rowData?: {
    name: string;
    description: string;
    status: string;
  };
  boxOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setParameterData: React.Dispatch<React.SetStateAction<ConsentParamaterFormInputs[]>>;
  parameterData: ConsentParamaterFormInputs[];
}
//CUSTOM PARAMETER-NO INTERFACE

//DASHBOARD
export interface ConsentRecordsTableProperties {
  subject_identity: string;
  geolocation: string;
  consent_status: string;
  consent_source: string;
  consent_date: string;
  consented_at: string;
  consent_purpose: string;
  processing_purpose: string;
}

export interface ConsentStatusProperties {
  Declined: number;
  Granted: number;
  Users: number;
  Withdrawn: number;
}

export interface ConsentStatusGraphProperties {
  declined: number;
  grant: number;
  month: string;
  withdrawn: number;
}

export interface ConsentByPurposeProperties {
  declined: number;
  grant: number;
  consent_purpose: string;
  withdrawn: number;
}

export interface ConsentByResidencyProperties {
  declined: number;
  grant: number;
  continent: string;
  withdrawn: number;
}

export interface EntityProperties {
  id: number;
  name: string;
  parent_id: number | null;
  customer_id: number;
  user_id: number;
  spoc_id: number | null;
  status: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

//BASIC INFORMATION
export type BasicInformationInputs = {
  endpointName: string;
  endpointUrl: string;
  businessUnit: string;
  department?: string;
};

export interface BasicInformationRowData {
  rowData?: {
    endpoint_title: string;
    endpoint_url: string;
    form_configured: string;
    business_unit?: string;
    department?: string;
  };
}
//FORM CENTRE
export type FormCentrePayment = {
  endpoint_title: string;
  endpoint_url: string;
  form_configured: string;
  business_unit: string;
};

//VIEW DETAILS
export interface ViewDetailsRowData {
  rowData?: {
    endpoint_title: string;
    endpoint_url: string;
    form_configured: string;
  };
}

//COLLECTION TEMPLATE

export type CollectionTemplatePayment = {
  template: string;
  type: string;
  entity: string;
  owner: string;
  modified: string;
  status: string;
};

//PREFERENCE CENTRE
export type PreferenceCentrePayment = {
  id: number;
  preference_center_name: string;
  published_date: string;
  owner: string;
  url: string;
  status: string;
};

//ADD PREFERENCE CENTRE STEPPER
export interface StepperFormActionsProperties {
  handleClick?: () => void;
}
//privacy-center-notice-mobile-view

export interface PrivacyCenterNoticeMobileViewSection {
  title: string;
  content: string;
  id: string;
}
//privacy-centre-notice
export interface PrivacyCentreNoticeSection {
  title: string;
  content: string | string[] | { [key: string]: string | string[] | PrivacyCentreNoticeSection };
}

//privacy-centre-details
export interface PrivacyNoteRequest {
  customer_id: number | undefined;
  entity_id: number | undefined;
  name: string | undefined;
  content: { title: string; content: string }[];
  privacy_note_id?: number;
}

//privacy-notice
export interface PrivacyNoticeRecordData {
  id: number;
  data_principal_id: number;
  consent_purpose_id: number;
  pii_label_id: number;
  pii_label_name: string;
  geolocation: string;
  continent: string;
  consent_status: string;
  consented_at: string;
  created_at: string;
  updated_at: string;
  frequency: string;
  consent_source: string;
  subject_identity: string;
  customer_id: number;
  name: string;
}

export interface PrivacyNotesDataProperties {
  id: number;
  customer_id: number;
  entity_id: number;
  name: string;
  version: number;
  status: boolean;
  created_at: string;
  updated_at: string;
}

//upload-template
export interface UploadCollectionTemplateProperties {
  onClose: (value: boolean) => void;
  ct_id: number;
  customer_id: number;
}

//ucm-forms-table
export type UCMFormsTableRowData = {
  id: string;
  title: string;
  description: string;
  created_at: string;
  updated_at: string;
  published: string;
};

export type UCMFormsTableData = {
  tableHeadings?: TableHeading[];
  data: UCMFormsTableRowData[];
  loading: boolean;
};

//subject-consent-list
export interface SubjectConsentListTableProperties {
  data_principal_id: number;
  subject_identity: string;
  record_data: PrivacyNoticeRecordData[];
}

//subject-consent-navbar
export type SUBJECT_CONSENT_NAV_PROPS = {
  selectedTab: number;
  setSelectedTab: React.Dispatch<React.SetStateAction<number>>;
};

//subject-details
export interface VaultData {
  vault_record_id: number;
  data_principal_id: number;
  pii_label_id: number;
  pii_label_name: string;
  pii_value: string;
  unified_person_id: number;
}

export interface SubjectIdentityData {
  data_principal_id: number;
  subject_identity: string;
  record_data: PrivacyNoticeRecordData[];
}

export interface ConsentRecordProperties {
  customer_id: number;
  collection_template_id: number;
  collection_template_name?: string;
  ucr_data: SubjectIdentityData[];
}

export interface SubjectListRecordData {
  unified_person_id: number;
  created_at?: string | null;
  up_person_uuid: string;
  current_data_principal_id: number;
  current_subject_identity: string;
  pii_data_vault_data: VaultData[];
  consent_record_data: ConsentRecordProperties[];
}

export interface ConsentStats {
  total_consents: number;
  database: number;
  api: number;
  manually: number;
  total_web_consents: number;
  web_form: number;
  web_preference_center: number;
  total_apk_consents: number;
  apk_form: number;
  apk_preference_center: number;
  email_form: number;
  email_preference_center: number;
  whatsapp_form: number;
  whatsapp_preference_center: number;
}

export interface ConsentCollectionTemplateData {
  collection_template_id: number;
  collection_template_name: string;
  active_data_principal_count: number;
}

export interface SubjectConsentHistory {
  data_principal_id: number;
  subject_identity: string;
  consent_source: string;
  status: string;
  geolocation: string | null;
  consented_at: string;
  pii_label_name?: string[];
}

export interface ConsentlistData {
  key: string;
  value: string;
}

export interface ConsentSourceData {
  Source: string;
  Count: number;
}

//user-logs-consent-details
export interface AuditLogData {
  id: number;
  data_principal_id: number;
  name: string;
  action_type: string;
  consent_status: string;
  consent_purpose_id: number;
  pii_label_id: number | null;
  consented_at: string | null;
  consent_expiration: string | null;
  geolocation: string | null;
  continent: string | null;
  frequency: string | null;
  change_time: string | null;
  action_description: string;
  original_action_type: string;
}

//subject-consent-types
export interface SubjectConsentTypesListTableProperties {
  collection_template_id: number;
  collection_template_name: string;
  total_users: number;
  entity_id: number;
  // ucr_data?: ProcessingPurposeData[];
  active_status: boolean;
}
export interface PiiList {
  id: number;
  name: string;
}

export interface ProcessingPurposeData {
  processing_purpose_name: string;
  processing_purpose_category_name: string;
  records: PrivacyNoticeRecordData[];
}

export interface SubjectConsentTypeIdentityData {
  data_principal_id: number;
  subject_identity: string;
  processing_purposes?: ProcessingPurposeData[];
  // processing_purpose_category_name: string;
  // record_data?: RecordData[];
}
//subject-concent-details
export interface ConsentRecord {
  id: number;
  data_principal_id: number;
  consent_purpose_id: number;
  pii_label_id: number;
  pii_label_name: string;
  geolocation: string;
  continent: string;
  consent_status: string;
  consented_at: string;
  consent_expiration: string;
  created_at: string;
  updated_at: string;
  frequency: string | null;
  consent_source: string;
  subject_identity: string;
  customer_id: number;
  name: string;
}

export interface SubjectPreferenceByUserProperties {
  collection_template_id: number;
  collection_template_name: string;
  data_principals: DataPrincipalUser[];
}

export interface DataPrincipalUser {
  subject_identity: string;
  data_principal_id: number;
  processing_purposes: ProcessingPurposeUser[];
}

export interface ProcessingPurposeUser {
  processing_purpose_name: string;
  processing_purpose_category_name: string;
  records: ConsentRecord[];
}

export interface DataPrincipalConsent {
  subject_identity: string;
  data_principal_id: number;
  records: ConsentRecord[];
}

export interface ProcessingPurposeConsent {
  processing_purpose_name: string;
  processing_purpose_category_name: string;
  collection_template_name: string;
  data_principals: DataPrincipalConsent[];
}

//navbar
export interface NavbarItems {
  navHeadings: string[];
  pathMapping: { [key: string]: string }; // Object to map headings to paths
  pathName: string;
  showSearch?: boolean; // Optional prop to show/hide search input
  showButton?: boolean; // Optional prop to show/hide button
  buttonName?: string; // Button name prop
  buttonHandler?: (value: boolean) => void; // Button click handler
  inputHandler?: (event: React.ChangeEvent<HTMLInputElement>) => void; // Input change handler
  showInputImage?: boolean; // Optional prop to show/hide image inside input
  inputImageSrc?: string; // Image source for the input field
  showButtonImage?: boolean; // Optional prop to show/hide image inside button
  buttonImageSrc?: string; // Image source for the button
  searchValue?: string | undefined;
  setSearchedValue?: React.Dispatch<React.SetStateAction<string | undefined>>;
}

//ucm-labs
export interface UCMLabsProperties {
  currentPath: string;
}

export interface ListData {
  id: number;
  name: string;
}

export interface NavbarConfigProperties {
  buttonName?: string;
  buttonHandler?: (value: boolean) => void;
  inputHandler?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  dialogTitle?: string;
  dialogContent?: React.ReactNode;
  dialogFooter?: React.ReactNode;
  dialogOpenHandler?: (open: boolean) => void;
  isDialogOpen?: boolean;
  dialogDescription?: string;
}

export interface IDeleteComponent {
  tab: string;
  id: number;
}
//add-processing-purpose-category
export type AddProcessingPurposeData = {
  id: number;
  name: string;
};

export type Purpose = {
  id: number;
  name: string;
  description: string;
  ai_generated: boolean;
  reviewed: boolean;
  processing_purpose_category: AddProcessingPurposeData | null;
};

export type PurposeData = {
  record_id: number;
  name: string;
  description: string;
  processing_purpose_category_id: string;
};

export interface ProcessingPurposeCellProperties {
  purpose: Purpose;
  reloadLabList: boolean;
  processingCategoryList: ListData[];
  setReloadLabList: (value: boolean) => void;
}

//processing-purpose
export type ProcessingPurposeProperties = {
  reloadLabList: boolean;
  searchValue: string | undefined;
};

export type PurposeDataProcessingPurpose = {
  record_id: number;
  name: string;
  description: string;
  processing_purpose_category_id: number | null;
};

//processing-category
export type Category = {
  id: number;
  name: string;
  description: string;
  ai_generated: boolean;
  reviewed: boolean;
  processing_purpose: AddProcessingPurposeData[] | null;
};

export type CategoryData = {
  record_id: number;
  name: string;
  description: string;
  related_processing_purposes: number[] | null;
};

//add-processing-purpose
export interface AddProcessingPurposeCellProperties {
  category_id: number;
  reloadLabList: boolean;
  purposes: AddProcessingPurposeData[];
  setReloadLabList: (value: boolean) => void;
}

//pii-label
export type PIILabelData = {
  id: number;
  name: string;
  description: string;
  ai_generated: boolean;
  reviewed: boolean;
  unique_subject_identifiable: boolean;
};

export type PIILabelProperties = {
  reloadLabList: boolean;
  searchValue: string | undefined;
};

export type UpdatePiiLabelData = {
  pii_name: string;
  record_id: number;
  description: string;
  unique_subject_identifiable: boolean;
};

//add-processing-purpose-category
export interface ProcessingPurposeCategoryCellProperties {
  purposes: AddProcessingPurposeData[];
}

//consent-purpose
export type ConsentPurposeData = {
  id: number;
  name: string;
  description: string;
  ai_generated: boolean;
  global_expiration_period: number;
  reviewed: boolean;
  processing_purpose: AddProcessingPurposeData | null;
};

export type UpdateConsentPurposeData = {
  consent_purpose_id: number;
  name: string;
  description: string;
  global_expiration_period: number|string;
};

//templates
export interface CollectionTemplateCompleteData {
  id: number;
  name: string;
  entity_id: number;
  customer_id: number;
  subject_identity_type_id: number;
  subject_identity_name: string;
  ct_cp_map: ConsentPurpose[];
  ct_pii_map: PIILabel[];
  owner_name: string;
  owner_email: string;
  active_status: boolean;
  status: boolean;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  searchedTerm: string | null;
}

export interface ConsentPurpose {
  customer_id: number;
  collection_template_id: number;
  collection_template_name: string;
  consent_purpose_id: number;
  consent_purpose_name: string | null;
  ct_cp_map: ProcessingPurpose[];
}

export interface ProcessingPurpose {
  id: number;
  processing_purpose_id: number;
  processing_purpose_name: string;
  pii_label_id: number | null;
  pii_label_name: string | null;
  frequency: string | null;
  consent_lifetime: number | null;
  status: boolean;
  created_at: string;
  updated_at: string;
}

export interface PIILabel {
  ct_pii_label_id: number;
  customer_id: number;
  collection_template_id: number;
  customer_pii_label_id: number;
  customer_pii_label_name: string;
  status: boolean;
  created_at: string;
  updated_at: string;
}

export interface FrequencyProperties {
  key: string;
  value: string;
}

export type CollectionTemplateData = {
  id: number;
  customer_id: number;
  customer_name: string;
  subject_identity_type_id: number;
  subject_identity_type_name: string;
  owner_name: string;
  owner_email: string;
  verification_key: string;
  active_status: false;
  name: string;
  entity_id: number;
  entity_name: string;
  pc_ack_mail: boolean;
  form_url: string | null;
  preference_centre_url: string | null;
  created_at: string | null;
  updated_at: string | null;
  form_source_id: number | null;
  form_id: number | null;
  preference_center_id: number | null;
};

//dynamic-section

export type DataType = Record<string, any> | Array<any>;

//add-option-dialog
export type ADD_OPTION_DIALOG_PROPS = {
  openModal: boolean;
  closeModal: () => void;
  label: string;
  title: string;
};

//basic-info-step
export interface DataPrincipleProperties {
  id: number;
  customer_id: number;
  pii_name: string;
  description: string;
}

export interface Process {
  id: number;
  name: string;
  parent_id: number | null;
  customer_id: number;
  department_id: number;
}

export interface Department {
  id: number;
  name: string;
  parent_id: number | null;
  customer_id: number;
  group_id: number;
}

export interface Vendor {
  id: number;
  name: string;
  customer_id: number;
  address: string;
  email: string;
  phone: string;
}

export interface BasicInfoProperties {
  id: number;
  customer_id: number;
  customer_name: string;
  subject_identity_type_id: number;
  entity_id: number;
  entity_name: string;
  subject_identity_type_name: string;
  owner_name: string;
  owner_email: string;
  verification_key: string;
  form_redirect_url: string | null;
  preference_center_redirect_url: string | null;
  active_status: boolean;
  name: string;
  template_owner_id: number;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
  form_id: number | null;
  form_url: string | null;
  available_translations: string | null;
  legal_framework_regulatory_id: number | null;
  legal_framework_regulatory_name: string | null;
  preference_center_list: any[]; // Update with specific type if known
  groups: any[] | null; // Update with specific type if known
  group_count: number;
  processes: number[] | null; // Array of process IDs or null
  process_count: number;
  departments: number[] | null; // Array of department IDs or null
  department_count: number;
  vendors: number[] | null; // Array of vendor IDs or null
  vendor_count: number;
}

export interface LegalFramework {
  id: number;
  legal_regulation_id: number;
  applicable_regulation: string;
  abbreviation: string;
  country: string;
  url: string;
  description: string;
  default_rentention_period: number;
  default_opt_out_allowded: boolean;
}

//consent-purpose-preference
export interface ConsentPurposeProperties {
  customer_id: number | undefined;
  ct_id: number;
}
export interface ConsentPurposeCollectionTemplateData {
  id: number;
  name: string;
  entity_id: number;
  customer_id: number;
  subject_identity_type_id: number;
  subject_identity_name: string;
  verification_key: string;
  ct_cp_map: ProcessingPurposeConsentPurpose[];
  ct_pii_map: PIILabelConsentPurpose[];
  owner_name: string;
  owner_email: string;
  active_status: boolean;
  status: boolean;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface ProcessingPurposeConsentPurpose {
  processing_purpose_id: number;
  processing_purpose_name: string;
  consent_bucket: ConsentPurposePreference[];
}

export interface ConsentPurposePreference {
  consent_purpose_id: number;
  consent_purpose_name: string;
  consent_purpose_description: string;
  pii_labels: (string | null)[];
  vendors: (string | null)[];
  processes: (string | null)[];
  ct_cp_map_bucket: ProcessingPurposeDetail[];
}

export interface ProcessingPurposeDetail {
  id: number;
  processing_purpose_id: number;
  processing_purpose_name: string;
  consent_purpose_id: number;
  consent_purpose_name: string;
  pii_label_id: number | null;
  pii_label: string | null;
  frequency: string | null;
  consent_lifetime: number | null;
  consent_expiration: string | null;
  default_opt_out_allowed: boolean;
  compulsory_consent: boolean;
  consent_status: boolean;
  backend_consent_status: boolean | null;
  manual_revoke_bool: boolean;
  manual_grant_bool: boolean;
  vendor_id: number | null;
  vendor_name: string | null;
  process_id: number | null;
  process_name: string | null;
}

export interface PIILabelConsentPurpose {
  pii_label_id: number;
  pii_label_name: string;
}

export interface StaticDataType {
  heading1: string;
  heading2: string;
  frequency: string;
  agreement_text: string;
  for: string;
  footer_content: string;
  buttonText: string;
  [key: string]: string; // Add index signature
}

//consent-purpose-step
export interface ConsentPurposeStep {
  consent_purpose_id: number;
  consent_purpose_name: string;
  processing_purpose_id: number;
  processing_purpose_name: string;
  processing_purpose_category_id: number;
  processing_purpose_category_name: string;
  pii_map: PIIMap[];
}

export interface PIIMap {
  pii_label_id: number;
  pii_label_name: string;
  ct_cp_map: CTCPMap[];
}

export interface CTCPMap {
  id: number;
  consent_purpose_id: number;
  consent_purpose_name: string;
  processing_purpose_id: number;
  processing_purpose_name: string;
  processing_purpose_category_id: number;
  processing_purpose_category_name: string;
  pii_label_id: number;
  pii_label_name: string;
  frequency: string;
  consent_lifetime: number;
  default_opt_out: boolean;
  compulsory_consent: boolean;
  manual_revoke_bool: boolean;
  manual_grant_bool: boolean;
  process_id: number | null;
  process_name: string | null;
  vendor_id: number | null;
  vendor_name: string | null;
  status: boolean;
}

export interface CTCPMapItem {
  processing_purpose_id: number;
  consent_purpose_id: number | null;
  pii_label_id: number;
  frequency?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  consent_lifetime: number;
  default_opt_out?: boolean;
  compulsory_consent?: boolean;
  vendor_ids?: number[];
  process_ids?: number[];
}

export interface CTCPRequestBody {
  customer_id: number;
  collection_template_id: number;
  pii_label_id_list: number[];
  ct_cp_map: CTCPMapItem[];
}

export interface NewConsentProperties {
  id: number | string;
  name: string;
  description: string;
  expiry?: number;
  created_at?: string;
  updated_at?: string;
  status?: boolean;
  processing_purpose_id?: number;
  processing_purpose_name?: string;
  processing_purpose_category_id?: number;
  processing_purpose_category?: string;
}
export interface ConsentPurposeRecordProperties {
  id: number | string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  status: boolean;
  global_expiration_period: number;
  processing_purpose_id: number;
  processing_purpose_name: string;
  processing_purpose_category_id: number;
  processing_purpose_category: string;
}
export interface NewProcessingPurposeProperties {
  id: any;
  name: any;
  description?: string;
  created_at?: string;
  updated_at?: string;
  status?: boolean;
  processing_purpose_id?: number;
  category_name?: string;
  category_id?: number;
}

export interface ProcessingPurposeRecordProperties {
  id: any;
  customer_id: number;
  customer_name: string;
  category_id: number;
  category_name: string;
  name: any;
  description: string;
  status: boolean;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}
export interface NewProcessingCategoryProperties {
  id: any;
  customer_id?: number;
  name: any;
  description?: string;
}

export interface ProcessingCategoryRecordProperties {
  id: any;
  customer_id: number;
  name: any;
  description: string;
}
export interface CollectionBuilderConsentProperties {
  processing_purpose_name: string | undefined;
  processing_purpose_id: number | string | undefined;
  consent_purpose_name: string | undefined;
  consent_purpose_id: number | string | undefined;
  processing_purpose_category: string | undefined;
  processing_purpose_category_id: number | string | undefined;
  pii_label_id_list?: PIILabelOptionProperties[];
  frequency?: string;
  consent_lifetime?: string;
}

export interface ConsentFrequencyProperties {
  key: string;
  value: string;
  name: string;
}

export interface PIILabelRecordsProperties {
  id: number;
  customer_id: number;
  customer_name: string;
  pii_name: string;
  description: string;
  unique_subject_identifiable: boolean;
  status: boolean;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface PIILabelOptionProperties extends Option {
  label: string;
  value: string;
}

export interface NewPIIProperties {
  name: string;
  description: string;
}

export interface CreateNewConsentPurposeProperties {
  customer_id: number | undefined; // MANDATORY
  collection_template_id: number | undefined; // MANDATORY
  cp_id: number | undefined | null; //OPTIONAL BELOW ALL
  cp_name: string | undefined | null;
  cp_description: string | undefined | null;
  cp_global_expiration_period: number | undefined | null;
  pp_id: number | undefined | null;
  pp_name: string | undefined | null;
  pp_description: string | undefined | null;
  ppc_id: number | undefined | null;
  ppc_name: string | undefined | null;
  ppc_description: string | undefined | null;
}

export interface PIIMapping {
  pii_id: number;
  pii_name: string;
  process: { id: number; name: string }[] | undefined;
  vendor: { id: number; name: string }[] | undefined;
}

//language-support-step

export interface LanguageSupportProcessingPurpose {
  id: number;
  name: string;
  description: string;
}

export interface LanguageSupportConsentPurpose {
  id: number;
  name: string;
  description: string;
}

export interface LanguageSupportPIILabel {
  id: number;
  name: string;
  description: string;
}

export interface CollectionTemplate {
  id: number;
  translation_record_id?: number;
  name: string;
}

export interface ProcessingPurposeCategory {
  id: number;
  name: string;
  description: string;
}

export interface BasicInfo {
  collection_template: CollectionTemplate;
  processing_purpose_categories: ProcessingPurposeCategory[];
  processing_purposes: LanguageSupportProcessingPurpose[];
  consent_purposes: LanguageSupportConsentPurpose[];
  pii_labels: LanguageSupportPIILabel[];
  message_for_display_type: string;
}

//preferences-form-preview
export interface PrivacyNoticeProps {
  id: number;
  customer_id: number;
  entity_id: number;
  name: string;
  version: number;
  status: true;
}
export interface PreferencePrivacySection {
  title: string;
  content: string;
}
export interface ConfigurationProperties {
  fontFamily: string;
  logoUrl: string;
  showLogo: boolean;
  showCookie: boolean;
  showConsent: boolean;
  showDSR: boolean;
  title: string;
  description: string;
  privacy_notice_heading: string;
  preference_center_heading: string;
  dsr_center_heading: string;
  dsrURL: string;
  dpoEmail: string;
  dsrContent: string;
}
export interface PreferenceFormPreviewProperties {
  setConfig?: React.Dispatch<React.SetStateAction<ConfigurationProperties>>;
  config?: ConfigurationProperties;
}

//preference-form-step
export interface PreferenceCenterConfiguration {
  language?: string;
  theme?: string;
  email_notifications_enabled?: boolean;
}

export interface PrivacyNoteContentQA {
  question: string;
  answer: string;
}

export interface PrivacyNoteContent {
  questions_and_answers?: PrivacyNoteContentQA[];
  data_policy?: string;
  terms_of_service_url?: string;
  version?: string;
}

export interface PrivacyNote {
  note_id?: number;
  content?: PrivacyNoteContent;
  last_updated?: string;
}

export interface PreferenceCenter {
  id: number;
  customer_id: number;
  subject_identity_type_id: number;
  entity_id: number;
  title: string;
  description: string;
  url: string | null;
  preference_center_configuration?: PreferenceCenterConfiguration | null;
  privacy_note?: PrivacyNote | null;
  need_cookie_center: boolean;
  need_dsr_center: boolean;
  status: boolean;
  published: boolean;
  published_at?: string | null;
  created_at: string;
  updated_at: string;
  owner_name: string;
  collection_template_ids: number[];
}

//source-step
export interface SourceProperties {
  key: string;
  value: string;
}
export interface SourceStepPIILabel {
  pii_label_id: number;
  pii_label_name: string;
}
export interface SourceStepProcessingPurpose {
  processing_purpose_id: number;
  processing_purpose_name: string;
  consent_bucket: SourceStepConsentPurpose[];
}
interface SourceStepConsentPurpose {
  consent_purpose_id: number;
  consent_purpose_name: string;
  consent_purpose_description: string;
  pii_labels: (string | null)[];
  vendors: (string | null)[];
  processes: (string | null)[];
  ct_cp_map_bucket: ProcessingPurposeDetail[];
}

export interface SourceStepCollectionTemplateData {
  id: number;
  name: string;
  entity_id: number;
  customer_id: number;
  subject_identity_type_id: number;
  subject_identity_name: string;
  verification_key: string;
  ct_cp_map: SourceStepProcessingPurpose[];
  ct_pii_map: SourceStepPIILabel[];
  owner_name: string;
  owner_email: string;
  active_status: boolean;
  status: boolean;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

//PAGES

//CONSENT-FORM-VERIFICATION
export interface DataProperties {
  id: number;
  subject_identity: string;
  inputValue: string;
}

//index.tsx
export interface BasicInfoType {
  title: string;
  description: string;
  pii_input_heading: string;
  preference_input_heading: string;
  form_footer_content: string;
}

export interface ConsentPurposeMapType {
  processing_purpose_id: number;
  processing_purpose_name: string;
  consent_bucket: ConsentListType[];
}

export interface ConsentListType {
  consent_purpose_id: number;
  consent_purpose_name: string;
  consent_purpose_description: string;
  pii_labels: string[];
  vendors: string[];
  processes: string[];
  ct_cp_map_bucket: CTCPMapBucket[];
}

export interface CTCPMapBucket {
  id: number;
  collection_template_id: number;
  processing_purpose_id: number;
  processing_purpose_name: string;
  consent_purpose_id: number;
  consent_purpose_name: string;
  pii_label_id: number;
  pii_label: string;
  frequency: string | null;
  consent_lifetime: number;
  consent_expiration: string | null;
  default_opt_out_allowed: boolean;
  compulsory_consent: boolean;
  consent_status: boolean;
  backend_consent_status: boolean | null;
  manual_revoke_bool: boolean;
  manual_grant_bool: boolean;
  vendor_id: number;
  vendor_name: string;
  process_id: number | null;
  process_name: string | null;
}

export interface PiiLabelMapType {
  pii_label_id: number;
  pii_label_name: string;
}

export interface AdditionalInfoType {
  form_id: number;
  customer_id: number;
  subject_identity_type_id: number;
  subject_identity_type_name: string;
  collection_template_id: number;
  collection_template_name: string;
  template_verfication_key: string;
  form_url: string | null;
  captcha_needed: string;
  form_redirect_url: string | null;
  is_published: boolean;
  published_at: string | null;
  created_at: string;
  updated_at: string;
  entity_id: number;
  active_status_of_collection_template: boolean;
  privacy_notice_id: number;
}
export interface ConsentFormDataType {
  additional_info: AdditionalInfoType;
  basic_info: BasicInfoType;
  form_configuration: any | null;
  pii_list: PiiLabelMapType[];
  purpose_list: ConsentPurposeMapType[];
}
export interface PiiValuesType {
  [key: number]: string;
}
export interface ResultType {
  data: ConsentFormDataType;
}

export interface ResponseType {
  success: boolean;
  status_code: number;
  message: string;
  result: ResultType;
  time: number;
}
export interface SubjectIdentityProperties {
  id: number;
  name: string;
}

export interface DataForVerificationProperties {
  id: number | undefined;
  subject_identity: string | undefined;
  inputValue: string | null;
}
export interface ConsentFormFrequencyProperties {
  key: string;
  value: string;
  name: string;
}

//Customization index.tsx
export interface FormListProperties {
  id: number;
  customer_id: number;
  subject_identity_type_id: number;
  subject_identity_name: string;
  collection_template_id: number;
  entity_id: number;
  title: string;
  description: string;
  url: string | null;
  redirect_url: string | null;
  captcha_needed: string; // Consider changing to `boolean` if "true"/"false" can be converted to boolean
  source_id: number | null;
  status: boolean;
  published: boolean;
  published_at: string | null; // ISO 8601 date string
  created_at: string; // ISO 8601 date string
  updated_at: string; // ISO 8601 date string
  deleted_at: string | null; // ISO 8601 date string
}

export interface PreferenceFormListProperties {
  id: number;
  customer_id: number;
  subject_identity_type_id: number;
  entity_id: number;
  title: string;
  description: string;
  url: string;
  need_cookie_center: boolean;
  need_dsr_center: boolean;
  status: boolean;
  published: boolean;
  published_at: string | null;
  created_at: string;
  updated_at: string;
  owner_name: string;
  collection_template_ids: number[];
}

export interface CustomizationProperties {
  pathName: string;
}

export interface Dsr {
  need_dsr_center: boolean;
  dsr_content: string | null;
}
export interface UcmPreferenceForm {
  customer_id: number;
  collection_template_id: number;
  collection_template_name: string;
  data_principal_id: number;
  purpose_list: PreferenceFormPurpose[];
}
export interface AdditionalInfo {
  preference_center_id: number;
  customer_id: number;
  subject_identity_type_id: number;
  subject_identity_type_name: string;
  entity_id: number;
  url: string;
  status: boolean;
  verification_key: string;
  redirect_URL: string | null;
  is_published: boolean;
  published_at: string; // Date string
  created_at: string; // Date string
  updated_at: string; // Date string
}

export interface PreferenceBasicInfo {
  title: string;
  description: string;
  privacy_note_version: number;
}
export interface PreferenceFormCenterConfiguration {
  fontFamily: string;
  logoUrl: string;
  showLogo: boolean;
  showCookie: boolean;
  showConsent: boolean;
  showDSR: boolean;
  showConsentFlow: boolean;
  title: string;
  description: string;
  privacy_notice_heading: string;
  preference_center_heading: string;
  dsr_center_heading: string;
  consent_flow_heading: string;
  dsrURL: string;
  dpoEmail: string;
  dsrContent: string;
  consent_purpose_configuration: {
    form: {
      logo: {
        show_logo: boolean;
        logo_url: string;
        width: string;
        height: string;
      };
      heading: {
        text: string;
        color: string;
        size: string;
      };
      description: {
        text: string;
        color: string;
        size: string;
      };
      font_family: string;
      color_scheme: string;
      border_radius: string;
      privacy_policy_url: string;
      show_privacy_policy_url: boolean;
      privacy_policy_url_text: string;
      show_language: boolean;
    };
    pii_section: {
      show_pii_section: boolean;
      show_labels: boolean;
      show_badges: boolean;
      heading: {
        text: string;
        color: string;
        size: string;
      };
      description: {
        text: string;
        color: string;
        size: string;
      };
    };
    consent_collection_section: {
      show_check_all_checkbox: boolean;
      all_checkbox_text: string;
      show_individual_checks: boolean;
      heading: {
        text: string;
        color: string;
        size: string;
      };
      description: {
        text: string;
        color: string;
        size: string;
      };
    };
    form_footer: {
      heading: {
        text: string;
        color: string;
        size: string;
      };
      description: {
        text: string;
        color: string;
        size: string;
      };
    };
    submit_button: {
      text: string;
      color: string;
      size: string;
    };
  };
  user_input_configuration: UserInputFormConfiguration;
  verify_input_configuration: VerifyInputFormConfiguration;
}
export interface PreferenceFormData {
  additional_info?: AdditionalInfo;
  basic_info?: PreferenceBasicInfo;
  preference_center_configuration?: PreferenceFormCenterConfiguration;
  privacy_note?: PrivacySection[];
  dsr?: Dsr;
  ucm_preference_form: UcmPreferenceForm[];
}
export interface PreferenceFormPurpose {
  processing_purpose_id: number;
  processing_purpose_name: string;
  consent_bucket: ConsentBucket[];
}

export interface ConsentBucket {
  consent_purpose_id: number;
  consent_purpose_name: string;
  consent_purpose_description: string;
  pii_labels: string[];
  vendors: string[];
  processes: string[];
  ct_cp_map_bucket: CTCPMapBucket[];
}
export interface PreferenceFormFrequencyProperties {
  key: string;
  value: string;
  name: string;
}
export interface SubjectIdentityDetailsProperties {
  subject_identity_type_id: number;
  subject_identity_type_name: string;
}
export interface FormSectionText {
  text: string;
  color: string;
  size: string;
}

export interface LogoConfig {
  show_logo: boolean;
  logo_url: string;
  width: string;
  height: string;
}

export interface HeadingDescriptionSection {
  heading: FormSectionText;
  description: FormSectionText;
}

export interface FormConfig extends HeadingDescriptionSection {
  logo: LogoConfig;
  font_family: string;
  color_scheme: string;
  border_radius: string;
  privacy_policy_url: string;
  show_privacy_policy_url: boolean;
  privacy_policy_url_text: string;
  show_language: boolean;
}

export interface PIISectionConfig extends HeadingDescriptionSection {
  show_pii_section: boolean;
  show_labels: boolean;
  show_badges: boolean;
}

export interface ConsentCollectionConfig extends HeadingDescriptionSection {
  show_check_all_checkbox: boolean;
  all_checkbox_text: string;
  show_individual_checks: boolean;
}

export interface FooterConfig extends HeadingDescriptionSection {}

export interface SubmitButtonConfig {
  text: string;
  color: string;
  size: string;
}

export interface FormConfiguration {
  form: FormConfig;
  pii_section: PIISectionConfig;
  consent_collection_section: ConsentCollectionConfig;
  form_footer: FooterConfig;
  submit_button: SubmitButtonConfig;
}

export interface UserInputFormConfiguration {
  logo: LogoConfig;
  heading: FormSectionText;
  description: FormSectionText;
  submit_button: SubmitButtonConfig;
  border_radius: string;
  font_family: string;
}

export interface VerifyInputFormConfiguration {
  logo: LogoConfig;
  heading: FormSectionText;
  description: {
    before_email: string;
    after_email: string;
    color: string;
    size: string;
  };
  submit_button: SubmitButtonConfig;
  secondary_button: SubmitButtonConfig;
  border_radius: string;
  font_family: string;
  authentication: boolean;
}
export interface DefaultWorkflowOption {
  id: number;
  name: string;
  description: string;
  workflow_mode: string;
  created_at: string;
}

export interface CustomWorkflowOption {
  id: number;
  flowtype: string;
  dsr_id: number;
  customer_id: number;
  workflow_status: string;
  module_name: string;
  created_at: string;
  updated_at: string;
}

export interface WorkflowData {
  id: number;
  workflow_name: string;
  custom_workflow_enabled: boolean;
}

export interface WorkflowDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (workflowData: WorkflowFormData) => void;
  mode: 'add' | 'edit';
  editWorkflowData?: WorkflowData;
  defaultWorkflowData?: { result: { data: DefaultWorkflowOption[] } };
  customWorkflowData?: { result: { data: CustomWorkflowOption[] } };
  isDefaultLoading?: boolean;
  isCustomLoading?: boolean;
  defaultError?: any;
  customError?: any;
}

export interface WorkflowFormData {
  workflowMode: 'webhook' | 'scheduler';
  workflowType: string;
  workflowTypeId: string; // Add the ID of the selected workflow option
  workflowPreference: 'default' | 'custom';
}
export interface WorkflowData {
  id: number;
  workflow_name: string;
  custom_workflow_enabled: boolean;
}
export interface StepConfig {
  label: string;
  description: string;
  component: React.ComponentType;
}
