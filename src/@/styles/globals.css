@tailwind base;
@tailwind components;
@tailwind utilities;

/* TipTap Editor Heading Styles - Only for headings created within the editor */
@layer components {
  /* Target only headings within TipTap editor content */
  .ProseMirror-focused h1,
  .ProseMirror h1 {
    font-size: 1.875rem !important; /* 30px */
    font-weight: 700 !important;
    margin: 0.5rem 0 !important;
    line-height: 1.2 !important;
  }

  .ProseMirror-focused h2,
  .ProseMirror h2 {
    font-size: 1.5rem !important; /* 24px */
    font-weight: 600 !important;
    margin: 0.5rem 0 !important;
    line-height: 1.3 !important;
  }

  .ProseMirror-focused h3,
  .ProseMirror h3 {
    font-size: 1.25rem !important; /* 20px */
    font-weight: 500 !important;
    margin: 0.25rem 0 !important;
    line-height: 1.4 !important;
  }

  .ProseMirror-focused h4,
  .ProseMirror h4 {
    font-size: 1.125rem !important; /* 18px */
    font-weight: 500 !important;
    margin: 0.25rem 0 !important;
    line-height: 1.4 !important;
  }

  .ProseMirror-focused h5,
  .ProseMirror h5 {
    font-size: 1rem !important; /* 16px */
    font-weight: 500 !important;
    margin: 0.25rem 0 !important;
    line-height: 1.5 !important;
  }

  .ProseMirror-focused h6,
  .ProseMirror h6 {
    font-size: 0.875rem !important; /* 14px */
    font-weight: 500 !important;
    margin: 0.25rem 0 !important;
    line-height: 1.5 !important;
  }
}

/* A4 Page Styles for Privacy Notice View */
@layer components {
  .a4-page {
    width: 100%;
    max-width: 310mm;
    min-height: 100%;
    flex: 1;
    padding: 0;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    font-family: 'Arial', sans-serif;
    font-size: 12pt;
    line-height: 1.6;
    color: #333;
    page-break-after: always;
    display: flex;
    flex-direction: column;
  }

  .a4-container {
    /* max-width: 350mm; */
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
    height: 100%;
    scroll-behavior: smooth;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }

  .a4-header {
    /* border-bottom: 2px solid #333; */
    /* margin-bottom: 20px; */
    padding-bottom: 10px;
  }

  .a4-title {
    font-size: 18pt;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
  }

  .a4-meta {
    font-size: 10pt;
    color: #666;
    margin-bottom: 5px;
  }

  .a4-section {
    margin-bottom: 20px;
    page-break-inside: avoid;
  }

  .a4-section-title {
    font-size: 14pt;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
    /* border-bottom: 1px solid #ccc; */
    padding-bottom: 5px;
  }

  .a4-section-content {
    font-size: 12pt;
    line-height: 1.6;
    text-align: justify;
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .a4-section-content p {
    margin-bottom: 10px;
  }

  .a4-section-content ul,
  .a4-section-content ol {
    margin-left: 20px;
    margin-bottom: 10px;
  }

  .a4-section-content li {
    margin-bottom: 5px;
  }

  .a4-print-button {
    position: fixed;
    top: 4px;
    left: 4px;
    z-index: 50;
    background: white;
    padding: 8px;
    border-radius: 6px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .a4-page-number {
    margin-bottom: 15px;
    padding-top: 10px;
  }

  .a4-page-footer {
    margin-top: auto;
    padding-top: 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 30px;
  }

  /* Print styles */
  @media print {
    .a4-container {
      background: white;
      padding: 0;
      scroll-behavior: auto;
    }

    .a4-page {
      box-shadow: none;
      margin: 0;
      page-break-after: always;
      page-break-inside: avoid;
    }

    .a4-page:last-child {
      page-break-after: auto;
    }

    .a4-page-footer {
      border-top: none;
      position: fixed;
      bottom: 10mm;
      width: 100%;
    }

    .no-print {
      display: none !important;
    }
  }

  /* TipTap Editor Styles for A4 Format */
  .a4-section-content .ProseMirror {
    outline: none !important;
    border: none !important;
    font-size: 12pt;
    line-height: 1.6;
    color: #333;
    min-height: 100%;
    height: 100%;
    padding: 20px;
    box-shadow: none !important;
    background: white;
    width: 100%;
  }

  .a4-section-content .ProseMirror:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
  }

  /* Ensure TipTap editor container takes full height */
  .a4-section-content .tiptap-editor {
    height: 100% !important;
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .a4-section-content .tiptap-editor > div {
    height: 100% !important;
    flex: 1 !important;
  }

  /* Remove any default borders and outlines from editor elements */
  .a4-section-content [contenteditable] {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
  }

  .a4-section-content [contenteditable]:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
  }

  .a4-section-content .ProseMirror p {
    margin-bottom: 10px;
  }

  .a4-section-content .ProseMirror h1,
  .a4-section-content .ProseMirror h2,
  .a4-section-content .ProseMirror h3,
  .a4-section-content .ProseMirror h4,
  .a4-section-content .ProseMirror h5,
  .a4-section-content .ProseMirror h6 {
    font-weight: bold;
    margin-bottom: 10px;
    margin-top: 20px;
    color: #333;
  }

  .a4-section-content .ProseMirror h1 {
    font-size: 18pt;
  }

  .a4-section-content .ProseMirror h2 {
    font-size: 16pt;
  }

  .a4-section-content .ProseMirror h3 {
    font-size: 14pt;
  }

  .a4-section-content .ProseMirror ul,
  .a4-section-content .ProseMirror ol {
    margin-left: 20px;
    margin-bottom: 10px;
  }

  .a4-section-content .ProseMirror li {
    margin-bottom: 5px;
  }

  .a4-section-content .ProseMirror strong {
    font-weight: bold;
  }

  .a4-section-content .ProseMirror em {
    font-style: italic;
  }

  .a4-section-content .ProseMirror u {
    text-decoration: underline;
  }

  /* Responsive adjustments */
  @media screen and (max-width: 768px) {
    .a4-page {
      width: 100%;
      min-height: auto;
      padding: 15px;
      margin: 0 0 10px 0;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
      border: 1px solid #ddd;
      page-break-after: always;
    }

    .a4-container {
      padding: 10px;
      background: #f5f5f5;
      overflow-y: auto;
      height: 100vh;
      scroll-behavior: smooth;
    }

    .a4-title {
      font-size: 16pt;
    }

    .a4-section-title {
      font-size: 13pt;
    }

    .a4-section-content {
      font-size: 11pt;
    }

    .a4-page-footer {
      margin-top: 15px;
      padding-top: 15px;
    }

    .a4-section-content .ProseMirror {
      font-size: 11pt;
    }
  }
}

/* @layer base { */
:root {
  --assign-icon: #000080;
  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;

  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;

  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;

  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;

  --card: 0 0% 100%;
  --card-foreground: 222.2 47.4% 11.2%;

  /* --primary: 222.2 47.4% 11.2%; */
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 240, 10%, 74%;
  /* --primary-foreground: 210 40% 98%; */

  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;

  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;

  --destructive: 0 100% 50%;
  --destructive-foreground: 210 40% 98%;

  --chart-1: 239, 100%, 62%;
  --chart-2: 239, 100%, 74%;
  --chart-3: 233, 100%, 83%;
  --chart-4: 155 59% 43%;
  --chart-5: 0 100% 64%;
  --chart-6: 40 100% 64%;
  --chart-7: 221 41% 51%;
  --chart-8: 221 49% 63%;
  --chart-blue-4: 242, 96%, 50%;
  --chart-blue-1: 239 100% 62%;
  --chart-blue-2: 239 100% 83%;
  --chart-blue-3: 233 100% 83%;
  --chart-green: 155 59% 43%;
  --chart-yellow: 40 100% 64%;
  --chart-red: 0 100% 64%;

  --risk-6: 155, 59%, 43%, 1;
  --risk-danger: 27, 87%, 67%;
  --risk-warning: 39, 100%, 50%;

  --ring: 215 20.2% 65.1%;

  --radius: 0.5rem;
}

.dark {
  --background: 224 71% 4%;
  --foreground: 213 31% 91%;

  --muted: 223 47% 11%;
  --muted-foreground: 215.4 16.3% 56.9%;

  --accent: 216 34% 17%;
  --accent-foreground: 210 40% 98%;

  --popover: 224 71% 4%;
  --popover-foreground: 215 20.2% 65.1%;

  --border: 216 34% 17%;
  --input: 216 34% 17%;

  --card: 224 71% 4%;
  --card-foreground: 213 31% 91%;

  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 1.2%;

  --secondary: 222.2 47.4% 11.2%;
  --secondary-foreground: 210 40% 98%;

  --destructive: 0 63% 31%;
  --destructive-foreground: 210 40% 98%;

  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;

  --ring: 216 34% 17%;

  --radius: 0.5rem;

  --main-text-color: black;

  --home_backgroud: linear-gradient(314deg, #424b50 14.86%, #0b101c 69.48%);
}

.light {
  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;

  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;

  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;

  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;

  --card: 0 0% 100%;
  --card-foreground: 222.2 47.4% 11.2%;

  /* --primary: 222.2 47.4% 11.2%; */
  /* --primary: #192440; */
  --primary: #0b101c;
  --primary-foreground: 240, 10%, 74%;
  /* --primary-foreground: 210 40% 98%; */

  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;

  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;

  --destructive: 0 100% 50%;
  --destructive-foreground: 210 40% 98%;

  --chart-1: 242 95% 42%;
  --chart-2: 236 100% 73%;
  --chart-3: 233 100% 83%;
  --chart-4: 155 59% 43%;
  --chart-5: 0 100% 64%;
  --chart-6: 39 100% 50%;

  --ring: 215 20.2% 65.1%;

  --radius: 0.5rem;

  /* --hover_effect: linear-gradient(314deg, #0847f7 14.86%, #0b101c 69.48%); */
  --hover-main-btn: linear-gradient(
    314deg,
    var(--primary) 14.86%,
    #0847f7 69.48%
  ); /* Button Hover Effect*/
  --sidebar-hover-btn: linear-gradient(
    314deg,
    var(--primary) 14.86%,
    #0847f7 69.48%
  ); /* hover Effect and Active Effect on*/
  --home-color-gradient: linear-gradient(
    314deg,
    #0847f7 14.86%,
    var(--primary) 69.48%
  ); /* hover Effect on cards and home gradient*/
  --sidebar-text-color: #ffff; /* Sidebar Text Color*/
  --sidebar-icons-color: #ffff; /* Sidebar Icons Color*/
  --sidebar-headings-text-color: #ffff; /* Sidebar Heading Text Color  (Account Setup/Theme/Data Mapping/Policy Mangagement etc.)*/
  --header-background-color: var(--primary);
  --header-text-color: #fff; /* Header Text Color*/
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }
}
